import os
import shutil
import sys

def check_and_remove_dir(dir_path, prompt_message):
    if os.path.exists(dir_path):
        response = input(f"{prompt_message} (y/n): ").lower()
        if response != 'y':
            print("操作已取消")
            sys.exit(0)
        try:
            shutil.rmtree(dir_path)
            print(f"已删除 {dir_path} 文件夹")
        except Exception as e:
            print(f"删除 {dir_path} 时发生错误: {e}")
            sys.exit(1)

def remove_corresponding_files_in_internal(obfdist_path, internal_path):
    """
    根据 obfdist 文件夹中的文件结构，删除 _internal 文件夹下对应路径的文件
    注意：不会删除 pyarmor_runtime_000000 文件夹，因为它是运行时必需的
    """
    if not os.path.exists(obfdist_path):
        print(f"警告: {obfdist_path} 不存在，跳过文件清理")
        return

    if not os.path.exists(internal_path):
        print(f"警告: {internal_path} 不存在，跳过文件清理")
        return

    removed_count = 0
    # 需要保留的文件夹列表
    protected_folders = ["pyarmor_runtime_000000"]

    # 遍历 obfdist 目录中的所有文件和文件夹
    for root, dirs, files in os.walk(obfdist_path):
        # 计算相对于 obfdist 的相对路径
        rel_path = os.path.relpath(root, obfdist_path)

        # 检查当前路径是否在受保护的文件夹内
        is_protected_path = False
        for protected_folder in protected_folders:
            if rel_path == protected_folder or rel_path.startswith(protected_folder + os.sep):
                is_protected_path = True
                break

        # 如果当前路径在受保护的文件夹内，跳过所有操作
        if is_protected_path:
            print(f"跳过受保护的路径: {rel_path}")
            continue

        # 如果是根目录，rel_path 会是 '.'，需要特殊处理
        if rel_path == '.':
            target_dir = internal_path
        else:
            target_dir = os.path.join(internal_path, rel_path)

        # 删除对应的文件
        for file in files:
            target_file = os.path.join(target_dir, file)
            if os.path.exists(target_file):
                try:
                    os.remove(target_file)
                    print(f"已删除: {target_file}")
                    removed_count += 1
                except Exception as e:
                    print(f"删除文件 {target_file} 时发生错误: {e}")

        # 删除对应的空文件夹（但跳过受保护的文件夹）
        for dir_name in dirs:
            # 检查是否是受保护的文件夹
            if dir_name in protected_folders:
                print(f"跳过受保护的文件夹: {dir_name}")
                continue

            target_subdir = os.path.join(target_dir, dir_name)
            if os.path.exists(target_subdir) and not os.listdir(target_subdir):
                try:
                    os.rmdir(target_subdir)
                    print(f"已删除空文件夹: {target_subdir}")
                except Exception as e:
                    print(f"删除文件夹 {target_subdir} 时发生错误: {e}")

    print(f"文件清理完成，共删除 {removed_count} 个文件")

def main():
    # 1. 检查 main.py 是否存在
    if not os.path.exists("main.py"):
        print("错误: main.py 文件不存在！")
        sys.exit(1)

    # 2. 检查并处理 obfdist 文件夹
    check_and_remove_dir("obfdist", "发现 obfdist 文件夹，是否删除并继续？")

    # 3. 检查并处理 dist/wowcker 文件夹
    check_and_remove_dir(os.path.join("dist", "wowcker"), "发现 dist/wowcker 文件夹，是否删除并继续？")

    # 4. 运行 pyarmor 命令
    print("\n开始执行 pyarmor 加密...")
    pyarmor_cmd = "pyarmor gen -O obfdist -r main.py chat/ database/ keyword_search/ knowledge_system/ login_window/"
    result = os.system(pyarmor_cmd)
    if result != 0:
        print("pyarmor 命令执行失败！")
        sys.exit(1)
    print("pyarmor 加密完成")

    # 5. 运行 pyinstaller 命令
    print("\n开始执行 PyInstaller 打包...")
    pyinstaller_cmd = "pyinstaller --clean wowcker_pack_mac.spec"
    result = os.system(pyinstaller_cmd)
    if result != 0:
        print("PyInstaller 打包失败！")
        sys.exit(1)
    print("PyInstaller 打包完成")

    # 6. 清理 _internal 文件夹中对应的文件
    print("\n开始清理 _internal 文件夹中的对应文件...")
    obfdist_path = "obfdist"
    internal_path = os.path.join("dist", "WowckerPlugin", "Contents", "Resources")
    remove_corresponding_files_in_internal(obfdist_path, internal_path)

    print("\n所有操作已完成！")

if __name__ == "__main__":
    main() 