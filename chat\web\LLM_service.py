from pathlib import Path
import os
import sys
import json
import time
import logging
import requests
from pathlib import Path
from typing import Dict, List
import re
import random
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 获取项目根目录
def get_project_root() -> Path:
    """获取项目根目录"""
    return Path(__file__).parent.parent.parent

# 确保导入路径包含项目根目录
project_root = str(get_project_root())
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入知识库搜索和模型API
from knowledge_system.src.knowledge_search import search_knowledge
from model.Qwen.qwen_api import get_qwen_response
from keyword_search.integration import enhanced_search_knowledge

# 获取当前脚本文件的绝对路径
current_file_path = Path(__file__)
# 获取当前脚本文件所在的目录
current_dir = current_file_path.parent


def get_random_fallback_response():
    """
    随机选择一个知识库外问题的回复话术
    """
    fallback_responses = [
        "对不起，一会儿我去问问我的主管，要不你先聊点别的你感兴趣的方面？",
        "这个问题，我不知道，我稍后去问问",
        "我是刚来的，这个我不是很了解，要不你先讲讲你想了解的产品，我尽可能先给您讲清楚，剩下的我不会的，一会儿我找找主管给你解决",
        "这个我确实不太清楚，让我先了解下你其他需要的，能帮到的我先帮你，这个问题我回头问问老员工",
        "不好意思，这块我经验不足，你看看还有什么其他问题吗？我先能解答的解答，这个我待会儿去请教下同事",
        "这个我还真不太懂，要不咱先聊聊你比较关心的产品方面，我熟悉的先给你介绍，不懂的我去问问领导",
        "抱歉这个我不太了解，你还有其他想咨询的吗？我先把能回答的给你说清楚，这个问题我去找有经验的同事问问"
    ]
    return random.choice(fallback_responses)


def load_prompt_with_pathlib(filepath):
    try:
        prompt = Path(filepath).read_text(encoding='utf-8')
        return prompt
    except FileNotFoundError:
        print(f"错误：找不到文件 {filepath}")
        return None
    except Exception as e:
        print(f"读取文件时发生错误：{e}")
        return None


# 构建提示词
prompt_filepath = current_dir / "prompt_template.txt"
prompt_template = load_prompt_with_pathlib(prompt_filepath)
# 构建改写提示词
res_rewrite_sys_prompt_path = current_dir / "res_rewrited_prompt.txt"
res_rewrite_sys_prompt = load_prompt_with_pathlib(res_rewrite_sys_prompt_path)
# 构建翻译提示词
res_trans_sys_prompt_path = current_dir / "res_trans_prompt.txt"
res_trans_sys_prompt = load_prompt_with_pathlib(res_trans_sys_prompt_path)
# 构建输入翻译提示词
input_trans_sys_prompt_path = current_dir / "input_trans_prompt.txt"
input_trans_sys_prompt = load_prompt_with_pathlib(input_trans_sys_prompt_path)


def generate_LLM_reply(
    message: str, 
    history, 
    store_context, 
    sender_id=0, 
    message_chz="",
    rag_mode="vector",  # 可选值: "enhanced", "vector", "none"
    translate=True, # 是否翻译
    rewrite=False,  # 是否改写
    split_sentences=True,    # 是否分句
    return_search_results=False  # 是否返回检索结果
):
    """
    生成AI回复
    
    Args:
        message: 用户消息内容
        history: 对话历史
        store_context: 店铺上下文信息
        sender_id: 发送者ID
        rag_mode: 检索方式，可选值: "enhanced"(增强检索), "vector"(向量检索), "none"(不使用RAG)
        translate: 是否进行翻译，默认True
        rewrite: 是否进行回答改写，默认False
        split_sentences: 是否将回复分段，默认True
        return_search_results: 是否在返回值中包含检索结果，默认False
        
    Returns:
        如果return_search_results为False:
            如果split_sentences为True，返回分段后的句子列表
            如果split_sentences为False，返回完整字符串回复
        如果return_search_results为True:
            返回(回复内容, 检索结果)的元组，回复内容格式同上
    """
    message_origin = message
    if message_chz:
        message = message_chz
    
    
    try:
        logger.info(f"为用户 {sender_id} 生成回复，检索模式:{rag_mode}, 翻译:{translate}, 改写:{rewrite}, 返回检索结果:{return_search_results}")
        
        # 根据选择的模式进行检索
        search_results = ""
        if rag_mode != "none":
            try:
                if rag_mode == "enhanced":
                    # 使用增强版搜索（结合BM25和向量检索）
                    search_results = enhanced_search_knowledge(message)
                    logger.info(f"使用增强版搜索（BM25+向量）成功")
                elif rag_mode == "vector":
                    # 使用原始向量搜索
                    search_results = search_knowledge(message)
                    logger.info(f"使用原始向量搜索成功")
            except Exception as e:
                # 如果选择的搜索方法失败，回退到原始向量搜索
                logger.warning(f"{rag_mode}搜索失败，回退到原始向量搜索: {str(e)}")
                search_results = search_knowledge(message)
        else:
            logger.info("未使用RAG检索")
        
        # 使用JSON结构组织提示词
        import json
        # 获取随机的回复话术
        random_fallback = get_random_fallback_response()

        # 构建提示词JSON结构
        prompt_json = {
            "messages": [
                {
                    "role": "system",
                    "content": {
                        "base_instruction": prompt_template.strip(),
                        "store_context": store_context.strip(),
                        "guidelines": [
                            "回答用户当前问题时，深度学习聊天记录history的内容，结合<history></history>内容，推理用户当前问题的意图，深度学习history中的内容思考后回答用户当前问题",
                            "知识库<rag></rag>中没有的产品和功能不要承诺和瞎说，诚实委婉表达不知道即可，禁止乱报价，乱报产品功能",
                            "用真实人类的反应回答，可以适当加一些语气词",
                            "对于相同或类似的问题，每次都使用不同的表达方式和句式结构回答，避免机械重复",
                            "每次回答时采用不同的语气和表达风格，有时热情活泼，有时专业稳重，有时亲切随和",
                            "必须使用使用中文回复"
                        ]
                    }
                },
                {
                    "role": "user",
                    "content": {
                        "history": history,
                        "knowledge": search_results if rag_mode != "none" else "",
                        "principle": "回答的时候句式一定不要和<history></history>中的内容重复，要灵活多变",
                        "style_guide": f"今天以{random.choice(['热情活泼', '稳重专业', '亲切随和', '简洁直接', '耐心详细'])}的风格回答问题",
                        "current_question": message
                    }
                }
            ],
            "parameters": {
                "response_format": {"type": "文本，一定不要出现json格式"}
            }
        }
        
        # 转换为JSON字符串
        prompt = json.dumps(prompt_json, ensure_ascii=False)        
        
        logger.info(f"生成JSON格式提示词，长度: {len(prompt)}")
        
        # 如果需要记录详细提示词，可以取消下面的注释
        # logger.debug(f"完整提示词JSON: {prompt}")
        
        # 使用通义千问API生成回复，调整参数以增强指令遵循
        response = get_qwen_response(
            prompt=prompt,
            temperature=0.1,  # 降低温度以增强确定性和指令遵循
            top_p=0.3,        # 降低top_p以减少随机性
            max_tokens=2000   # 适当限制输出长度
        )
        logger.info(f"原始回复: {response}")
        
        result = response
        
        # 是否进行翻译
        if translate:
            result = translate_response(result, message_origin, history)
            logger.info(f"执行翻译，翻译结果为{result}")

        # 是否进行回答改写
        if rewrite:
            origin = result
            result = rewrite_response(result, history)
            logger.info(f"执行改写，原文为{origin}\n改写结果为{result}")
        
        # 准备最终返回的回复内容
        final_reply = split_by_sentences(result) if split_sentences else result
        
        # 创建回复信息的JSON摘要
        reply_summary = {
            "original_response": response,
            "translated_response": result if translate else None,
            "rewritten_response": result if rewrite else None,
            "final_reply": final_reply,
            "search_results": search_results if search_results else None,
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"回复摘要: {json.dumps(reply_summary, ensure_ascii=False)}")
        
        # 根据是否需要返回检索结果来返回不同格式的结果
        if return_search_results:
            return (final_reply, search_results)
        else:
            return final_reply, reply_summary
        
    except Exception as e:
        logger.exception(f"生成回复时出错: {str(e)}")
        error_msg = "非常抱歉，我有点急事，回头给您答复。"
        if return_search_results:
            return (error_msg, "")
        else:
            return error_msg


def split_by_sentences(text: str) -> list[str]:
    # 正则表达式匹配中文和英文的句末标点，并确保标点符号保留在句子末尾
    # (?<=[。？！.?!\n]) 是一个正向后行断言，它匹配紧跟在指定标点或换行符之后的位置
    # \s* 匹配标点符号或换行符后的任何空白字符（包括没有空白字符的情况），并在分割时移除它们
    sentences = re.split(r'(?<=[。？！?!;；\n])\s*', text)

    # 清理掉可能因为连续标点或文本首尾标点产生的空字符串
    sentences = [s.strip() for s in sentences if s and s.strip()]
    return sentences


def rewrite_response(response, history=""):
    """使用LLM改写回复内容"""
    try:
        # 解析history字符串，提取系统回复内容
        system_replies = []
        if history and isinstance(history, str):
            # 完全重新设计解析方法
            history_entries = []
            lines = history.split('\n')
            current_type = None
            current_content = []
            
            # 第一步：根据标签将历史记录分割成不同的条目
            for line in lines:
                if line.startswith("客户问题："):
                    # 如果已经有内容，则保存之前的条目
                    if current_type and current_content:
                        entry = {
                            "type": current_type,
                            "content": "\n".join(current_content).strip()
                        }
                        history_entries.append(entry)
                    
                    # 开始新的用户问题条目
                    current_type = "客户问题"
                    current_content = [line[5:].strip()]
                
                elif line.startswith("系统回复："):
                    # 如果已经有内容，则保存之前的条目
                    if current_type and current_content:
                        entry = {
                            "type": current_type,
                            "content": "\n".join(current_content).strip()
                        }
                        history_entries.append(entry)
                    
                    # 开始新的系统回复条目
                    current_type = "系统回复"
                    current_content = [line[5:].strip()]
                
                elif current_type:  # 如果当前行不是标签行但我们正在收集内容
                    current_content.append(line.strip())
            
            # 保存最后一个条目（如果有）
            if current_type and current_content:
                entry = {
                    "type": current_type,
                    "content": "\n".join(current_content).strip()
                }
                history_entries.append(entry)
            
            # 第二步：筛选出所有的系统回复
            for entry in history_entries:
                if entry["type"] == "系统回复":
                    system_replies.append(entry["content"])
            
            logger.info(f"从历史记录中提取了 {len(system_replies)} 条系统回复")
            
            # 输出调试信息
            for i, reply in enumerate(system_replies):
                logger.debug(f"系统回复 {i+1}: {reply}")

        # 如果新回复不在历史系统回复中，则不需要改写
        if response not in system_replies:
            logger.info(f"回复不重复，无需进行改写")
            return response
        
        # 原有的改写逻辑
        res_rewrite_prompt = f"""
当前对话上下文: 
{history}
----
客服的本条回答： 
输入：{response}
输出："""
        # 调用LLM进行改写
        res_rewrited = get_qwen_response(prompt=res_rewrite_prompt, 
                                         system_prompt=res_rewrite_sys_prompt, 
                                         temperature=1, 
                                         top_p=0.95)
        logger.info(f"执行改写，原文为{response}")
        
        # 解析[[]]中的内容
        import re
        pattern = r"\[\[(.*?)\]\]"
        match = re.search(pattern, res_rewrited)
        
        if match:
            # 提取[[]]内的内容
            extracted_content = match.group(1)
            logger.info(f"改写结果为 {extracted_content}")
            return extracted_content
        else:
            # 如果没有找到[[]]格式，返回原始结果
            logger.info(f"未找到[[]]格式，改写失败，返回原始结果")
            return response
    except Exception as e:
        logger.exception(f"改写回复时出错: {str(e)}")
        return response  # 如果发生错误，返回原始回复


# 保存上一次检测到的用户语言，用于维持语言连续性
_last_detected_language = None

def translate_response(response, question, history=None):
    """使用通义千问进行两步处理：先判断语言是否需要翻译，再进行翻译"""
    global _last_detected_language
    try:
        # 文本预处理函数：过滤标点符号和数字，保留有意义的文本内容
        def preprocess_text(text):
            import re
            # 先过滤掉系统生成的消息格式
            system_message_patterns = [
                r'一条.*?类型的消息',
                r'一条.*?类型的消息'
            ]

            filtered_text = text
            for pattern in system_message_patterns:
                filtered_text = re.sub(pattern, '', filtered_text)

            # 过滤邮箱地址
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            filtered_text = re.sub(email_pattern, '', filtered_text)

            # 过滤电话号码（支持多种格式）
            phone_patterns = [
                r'\+86[-.\s]?1[3-9]\d{9}',          # 中国手机号 +86 格式
                r'1[3-9]\d{9}',                     # 中国手机号
                r'\d{3,4}[-.\s]?\d{7,8}',           # 中国固话
                r'\d{3}-\d{3}-\d{4}',               # ************
                r'\d{3}\.\d{3}\.\d{4}',             # ************
                r'\d{3}\s\d{3}\s\d{4}',             # ************
                r'\(\d{3}\)\s?\d{3}-?\d{4}',        # (************* 或 (123)456-7890
                r'\+?1?[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}',  # 各种美国格式
                r'\d{10,15}',                       # 通用长数字串（可能是电话号码）
                r'\d{3}[-.\s]\d{4}[-.\s]\d{4}',     # 138-1234-5678 格式
                r'\d{3}[-.\s]\d{8}',                # 138-12345678 格式
            ]

            for pattern in phone_patterns:
                filtered_text = re.sub(pattern, '', filtered_text)

            # 保留字母、空格和基本CJK字符，过滤标点和数字
            return re.sub(r'[^\w\s\u4e00-\u9fff\u3040-\u30ff\uac00-\ud7a3]', ' ', filtered_text).strip()
        
        # 预处理当前问题
        preprocessed_question = preprocess_text(question)
        
        # 准备上下文，包括历史记录中的用户问题
        language_context = ""
        if history and isinstance(history, str):
            # 提取历史记录中的用户问题作为语言判断依据
            history_lines = history.split('\n')
            user_questions = []

            # 定义可能的用户问题前缀
            user_question_prefixes = [
                '客户的历史提问：',
                '用户历史问题：',
                '用户提问：',
                '用户问题：'
            ]

            for line in history_lines:
                for prefix in user_question_prefixes:
                    if line.startswith(prefix):
                        user_question = line[len(prefix):].strip()
                        if user_question:
                            user_questions.append(user_question)
                            break

            # 使用最近的几个问题，增加到5个以获取更多上下文
            recent_questions = user_questions[-5:] if user_questions else []
            if recent_questions:
                # 对历史问题也进行预处理
                preprocessed_questions = [preprocess_text(q) for q in recent_questions]
                # 过滤掉预处理后为空的问题
                preprocessed_questions = [q for q in preprocessed_questions if q]
                language_context = "\n".join(preprocessed_questions)
        
        # 特殊情况处理：纯数字或极短输入的语言判断
        import re
        is_special_input = False
        
        # 检查输入是否是纯数字、极短输入或预处理后为空
        if re.match(r'^\d+$', question) or len(question.strip()) <= 5 or not preprocessed_question:
            is_special_input = True
            logger.info(f"检测到特殊输入 (纯数字或短文本): {question}")
        
        # 第一步：使用简化的语言分析
        import json

        # 构建简化的语言分析内容
        analysis_text = preprocessed_question
        if language_context:
            analysis_text = f"{preprocessed_question}\n{language_context}"

        # 为特殊输入添加历史语言提示
        special_hint = ""
        if is_special_input and _last_detected_language:
            special_hint = f"历史语言: {_last_detected_language}"

        language_check_prompt_json = {
            "text_to_analyze": analysis_text,
            "special_hint": special_hint,
            "task": "判断文本主要使用的语言，返回语言名称"
        }
        
        language_check_result = get_qwen_response(
            prompt=json.dumps(language_check_prompt_json, ensure_ascii=False),
            system_prompt="你是语言识别专家。分析文本使用的主要语言，忽略数字和标点符号。如果文本为空或无法判断，参考special_hint中的历史语言，否则默认为English。直接返回语言名称，如：English、中文、日本语等。",
            temperature=0.1,
            top_p=0.5
        )
        
        # 解析结果 - 现在AI直接返回语言名称
        # 默认语言：如果有历史记录则使用上次检测到的语言，否则默认为中文
        user_language = _last_detected_language if _last_detected_language else "英文"

        # 清理AI返回的结果
        detected_language = language_check_result.strip()

        # 只有在非特殊输入或明确检测到语言时更新用户语言
        if detected_language and detected_language != "未知" and detected_language != "无法判断":
            if not is_special_input or detected_language != "English":  # 特殊输入时，只有非默认语言才更新
                user_language = detected_language
                # 更新上次检测到的语言
                _last_detected_language = user_language
        
        logger.info(f"语言分析结果 - 用户语言: {user_language}")
        
        # 检查系统回复语言是否需要翻译
        # 这里我们假设系统默认回复是中文，如果用户语言非中文，则需要翻译
        if user_language.lower() in ['中文', 'chinese', '汉语', '普通话', '国语']:
            # 用户语言是中文，不需要翻译
            return response
        
        # 第二步：进行翻译
        translation_prompt_json = {
            "text": response,
            "target_language": user_language,
            "task": f"将以上文本翻译成{user_language}，保持原文语气和风格，直接返回翻译结果"
        }
        
        translated_response = get_qwen_response(
            prompt=json.dumps(translation_prompt_json, ensure_ascii=False),
            system_prompt=f"你是一个专业的{user_language}翻译专家。请直接返回翻译结果，不要添加任何解释或JSON结构。",
            temperature=0.3,
            top_p=0.7
        )
        
        logger.info(f"翻译完成 (用户语言: {user_language}): {translated_response[:50]}...")

        # 清理翻译结果（移除可能的额外文本）
        translated_clean = translated_response.strip()

        # 增强验证翻译是否成功
        if translated_clean == response:
            logger.warning(f"翻译成{user_language}可能失败，返回原始回复")
            return response

        # AI回复翻译不再扣费 - 已改为免费服务
        logger.info(f"AI回复翻译完成，目标语言: {user_language} (免费服务)")

        return translated_clean
            
    except Exception as e:
        logger.exception(f"翻译过程出错: {str(e)}")
        return response  # 如果发生错误，返回原始回复


def input_trans(message: str):
    """
    将输入消息翻译为中文
    
    Args:
        message: 用户输入的消息内容
        
    Returns:
        str: 翻译后的中文消息
    """
    try:
        # 定义语言检测函数
        def detect_cjk_language(text):
            # 中文汉字范围
            has_chinese_char = any('\u4e00' <= ch <= '\u9fff' for ch in text)
            
            # 日语特有假名检测
            has_japanese_kana = any(('\u3040' <= ch <= '\u309f') or ('\u30a0' <= ch <= '\u30ff') for ch in text)
            
            # 韩语谚文检测
            has_korean_char = any('\uac00' <= ch <= '\ud7a3' for ch in text)
            
            # 判断逻辑
            if has_japanese_kana:
                return "japanese"  # 含有日语假名，判定为日语
            elif has_korean_char:
                return "korean"    # 含有韩语谚文，判定为韩语
            elif has_chinese_char:
                return "chinese"   # 只有汉字，判定为中文
            else:
                return "other"     # 其他语言
        
        # 检查消息语言
        language = detect_cjk_language(message)
        
        # 如果已经是中文，无需翻译
        if language == "chinese":
            logger.info(f"消息已经是中文，无需翻译: {message[:50]}...")
            return message
        
        # 替换提示词中的[用户消息]占位符
        trans_prompt = input_trans_sys_prompt.replace("[用户消息]", message)
        
        # 调用LLM进行翻译
        translated_message = get_qwen_response(
            prompt=trans_prompt,
            system_prompt="",  # 无需系统提示词，因为提示词模板已包含完整指令
            temperature=0.2,
            top_p=0.3
        )
        
        logger.info(f"输入翻译，原文: {message[:50]}...")
        logger.info(f"输入翻译，译文: {translated_message[:50]}...")
        
        # 验证翻译结果是否为中文
        is_translation_chinese = any('\u4e00' <= ch <= '\u9fff' for ch in translated_message)
        
        if is_translation_chinese:
            # 记录来信翻译积分
            try:
                from config.points_manager import get_points_config_manager
                from database.app_db_manager import AppDBManager
                from database.store_operations import StoreOperations

                points_config = get_points_config_manager()

                # 计算来信翻译积分
                translation_points = points_config.get_ai_service_points('incoming_translation_points')

                if translation_points > 0:
                    # 更新stores表积分
                    store_ops = StoreOperations()
                    stores = store_ops.get_all_stores()
                    if stores:
                        first_store = stores[0]
                        store_id = first_store['id']
                        translation_points_storage = points_config.convert_points_for_storage(translation_points)
                        store_ops.update_points_atomically(store_id, translation_points_storage)

                    # 记录到points表
                    from database.points_operations import get_current_username

                    db_manager = AppDBManager()
                    db_manager.initialize()

                    username = get_current_username()
                    point_type = points_config.get_point_type_name('ai_translation')
                    description = f"来信翻译为中文"

                    db_manager.add_points_atomically(username, point_type, translation_points, description)
                    logger.info(f"来信翻译积分记录已添加: {translation_points} 积分，用户: {username}")

            except Exception as points_error:
                logger.warning(f"记录来信翻译积分失败: {str(points_error)}")

            return translated_message
        else:
            # 翻译结果不是中文，尝试二次翻译
            logger.warning(f"翻译结果不是中文，尝试二次翻译: {translated_message[:50]}...")
            
            # 强调必须翻译成中文
            retry_trans_prompt = """# 任务：将以下文本翻译成简体中文

输入文本：
{}

输出（仅输出中文翻译结果）：""".format(message)
            
            # 二次尝试
            retry_translated_message = get_qwen_response(
                prompt=retry_trans_prompt,
                system_prompt="你是一个翻译器，必须将输入文本翻译成简体中文",
                temperature=0.1,
                top_p=0.1
            )
            
            # 再次验证结果
            is_retry_chinese = any('\u4e00' <= ch <= '\u9fff' for ch in retry_translated_message)
            
            if is_retry_chinese:
                logger.info(f"二次翻译成功: {retry_translated_message[:50]}...")

                # 记录来信翻译积分
                try:
                    from config.points_manager import get_points_config_manager
                    from database.app_db_manager import AppDBManager
                    from database.store_operations import StoreOperations

                    points_config = get_points_config_manager()

                    # 计算来信翻译积分
                    translation_points = points_config.get_ai_service_points('incoming_translation_points')

                    if translation_points > 0:
                        # 更新stores表积分
                        store_ops = StoreOperations()
                        stores = store_ops.get_all_stores()
                        if stores:
                            first_store = stores[0]
                            store_id = first_store['id']
                            translation_points_storage = points_config.convert_points_for_storage(translation_points)
                            store_ops.update_points_atomically(store_id, translation_points_storage)

                        # 记录到points表
                        from database.points_operations import get_current_username

                        db_manager = AppDBManager()
                        db_manager.initialize()

                        username = get_current_username()
                        point_type = points_config.get_point_type_name('ai_translation')
                        description = f"来信翻译为中文"

                        db_manager.add_points_atomically(username, point_type, translation_points, description)
                        logger.info(f"来信翻译积分记录已添加: {translation_points} 积分，用户: {username}")

                except Exception as points_error:
                    logger.warning(f"记录来信翻译积分失败: {str(points_error)}")

                return retry_translated_message
            else:
                # 如果两次都失败，则返回原始消息
                logger.warning(f"两次翻译均失败，返回原文")
                return message
    except Exception as e:
        logger.exception(f"翻译输入时出错: {str(e)}")
        return message  # 如果发生错误，返回原始消息