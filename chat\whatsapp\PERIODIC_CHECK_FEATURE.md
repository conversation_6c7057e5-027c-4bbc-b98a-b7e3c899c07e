# 定期主动拉取未读消息功能

## 功能概述
为了解决网络不稳定导致的WhatsApp消息丢失问题，新增了定期主动拉取未读消息的功能。

## 解决的问题
- **网络波动**：短暂的网络中断可能导致错过实时消息
- **连接不稳定**：wwebjs依赖浏览器连接，可能存在消息接收延迟
- **消息丢失**：被动监听可能错过某些消息

## 功能特点

### 1. 智能定期检查
- **检查间隔**：每1分钟检查一次（可配置）
- **防重复**：如果上次检查还在进行中，会跳过本次检查
- **自动启动**：客户端准备就绪后自动开始定期检查

### 2. 详细日志输出
```
🔍 开始定期未读消息检查 (距离上次检查: 60 秒)
📊 获取到 25 个聊天会话
📨 发现 3 个聊天有未读消息
✅ 未读消息检查完成: 处理 5 条新消息，跳过 2 条重复消息，耗时 1.23 秒
```

### 3. 智能资源管理
- **条件检查**：只有发现未读消息时才进行详细处理
- **错误恢复**：检查失败不会影响正常的消息监听
- **自动清理**：客户端断开连接时自动停止定期检查

## 配置选项

### 在 `chat/whatsapp/index.js` 中的配置
```javascript
// 定期检查未读消息的配置
const PERIODIC_CHECK_INTERVAL = 60000; // 1分钟（毫秒）
const PERIODIC_CHECK_ENABLED = true; // 是否启用定期检查
```

### 可调整参数
- **检查间隔**：建议30秒-5分钟之间
- **启用状态**：可以完全禁用此功能
- **检查超时**：防止单次检查时间过长

## 工作流程

### 1. 启动流程
```
客户端准备就绪 → 启动定期检查 → 设置定时器
```

### 2. 检查流程
```
定时器触发 → 检查是否正在执行 → 获取所有聊天 → 筛选未读聊天 → 处理未读消息 → 记录统计信息
```

### 3. 停止流程
```
客户端断开连接 → 清理定时器 → 停止定期检查
```

## 与现有功能的协作

### 1. 不冲突
- 定期检查与实时消息监听并行工作
- 使用相同的消息去重机制，避免重复处理
- 不影响现有的发送和接收逻辑

### 2. 互补增强
- **实时监听**：处理正常的消息接收
- **定期检查**：补充可能错过的消息
- **共同保障**：确保消息接收的完整性

## 性能影响

### 1. 资源消耗
- **CPU**：每分钟短暂的检查操作，影响极小
- **内存**：复用现有的消息处理逻辑，无额外内存开销
- **网络**：每分钟一次API调用，流量消耗很少

### 2. 优化措施
- **智能跳过**：没有未读消息时快速返回
- **防重复**：避免并发检查导致的资源浪费
- **错误隔离**：检查失败不影响正常功能

## 使用建议

### 1. 默认配置
- 大多数情况下使用默认的1分钟间隔即可
- 保持功能启用状态

### 2. 特殊场景调整
- **网络不稳定环境**：可以缩短到30秒间隔
- **消息量很大**：可以延长到2-3分钟间隔
- **资源受限环境**：可以临时禁用此功能

### 3. 监控建议
- 观察日志中的检查频率和处理结果
- 注意是否有重复处理的消息
- 关注检查耗时，如果过长可能需要优化

## 故障排除

### 1. 常见问题
- **检查频率过高**：调整 `PERIODIC_CHECK_INTERVAL`
- **重复处理消息**：检查消息去重逻辑
- **检查失败**：查看错误日志，可能是网络问题

### 2. 调试方法
- 查看控制台日志中的定期检查信息
- 使用测试脚本验证功能是否正常
- 临时禁用功能来排除问题

## 总结
这个功能通过定期主动检查的方式，有效解决了被动监听可能错过消息的问题，提高了WhatsApp消息接收的可靠性，同时保持了对现有功能的完全兼容。
