"""
WhatsApp聊天客服模块
负责连接WhatsApp Web，接收消息并自动回复
"""
import os
import sys
import json
import time
import logging
import threading
import platform
import shutil
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 获取项目根目录
def get_project_root() -> Path:
    """获取项目根目录"""
    return Path(__file__).parent.parent.parent

# 确保导入路径包含项目根目录
project_root = str(get_project_root())
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入知识库搜索和模型API
from knowledge_system.src.knowledge_search import search_knowledge
from model.Qwen.qwen_api import get_qwen_response
from database.store_operations import StoreOperations
from database.context_operations import ContextOperations
from database.db_manager import DatabaseManager
from database.intent_operations import IntentOperations

# 导入聊天服务接口
from chat.chat_interface import ChatInterface
from chat.web.LLM_service import generate_LLM_reply
from chat.web.LLM_service import input_trans
from chat.whatsapp.intention_detection import analyze_customer_intent_hybrid
from chat.whatsapp.automation_rules_manager import automation_rules_manager
from abc import ABC, abstractmethod

# 导入锁管理器
try:
    from lock_manager import with_lock
    LOCK_MANAGER_AVAILABLE = True
except ImportError:
    # 如果锁管理器不可用，使用原始锁机制作为后备
    import contextlib
    @contextlib.contextmanager
    def with_lock(lock_name: str, timeout: float = 5.0):
        # 后备方案：不使用锁管理器
        yield
    LOCK_MANAGER_AVAILABLE = False

# 全局回调函数，用于处理店铺状态异常
_store_status_callback = None

def register_store_status_callback(callback):
    """
    注册店铺状态异常回调函数
    
    Args:
        callback: 回调函数，接收店铺名称参数
    """
    global _store_status_callback
    _store_status_callback = callback
    
def handle_store_status_exception(store_name):
    """
    处理店铺状态异常

    Args:
        store_name: 店铺名称
    """
    global _store_status_callback
    if _store_status_callback:
        _store_status_callback(store_name)

def find_chrome_executable():
    """
    跨平台查找Chrome浏览器可执行文件

    Returns:
        str: Chrome可执行文件路径，如果未找到则返回None
    """
    system = platform.system().lower()

    if system == 'darwin':  # macOS
        possible_paths = [
            '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
            '/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary',
            '/Applications/Chromium.app/Contents/MacOS/Chromium',
            '/usr/bin/google-chrome-stable',
            '/usr/bin/google-chrome',
            '/usr/bin/chromium-browser',
            '/usr/bin/chromium'
        ]
    elif system == 'windows':  # Windows
        program_files = os.environ.get('ProgramFiles', 'C:\\Program Files')
        program_files_x86 = os.environ.get('ProgramFiles(x86)', 'C:\\Program Files (x86)')
        local_app_data = os.environ.get('LOCALAPPDATA', os.path.join(os.path.expanduser('~'), 'AppData', 'Local'))

        possible_paths = [
            os.path.join(program_files, 'Google', 'Chrome', 'Application', 'chrome.exe'),
            os.path.join(program_files_x86, 'Google', 'Chrome', 'Application', 'chrome.exe'),
            os.path.join(local_app_data, 'Google', 'Chrome', 'Application', 'chrome.exe'),
            os.path.join(program_files, 'Google', 'Chrome Beta', 'Application', 'chrome.exe'),
            os.path.join(program_files_x86, 'Google', 'Chrome Beta', 'Application', 'chrome.exe'),
            os.path.join(local_app_data, 'Google', 'Chrome Beta', 'Application', 'chrome.exe'),
            os.path.join(program_files, 'Google', 'Chrome Dev', 'Application', 'chrome.exe'),
            os.path.join(program_files_x86, 'Google', 'Chrome Dev', 'Application', 'chrome.exe'),
            os.path.join(local_app_data, 'Google', 'Chrome Dev', 'Application', 'chrome.exe'),
            os.path.join(local_app_data, 'Chromium', 'Application', 'chrome.exe')
        ]
    else:  # Linux
        possible_paths = [
            '/usr/bin/google-chrome-stable',
            '/usr/bin/google-chrome',
            '/usr/bin/chromium-browser',
            '/usr/bin/chromium',
            '/snap/bin/chromium',
            '/usr/local/bin/chrome',
            '/usr/local/bin/chromium'
        ]

    # 检查每个可能的路径
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"找到Chrome浏览器: {path}")
            return path

    # 尝试使用which命令查找
    try:
        if system == 'windows':
            # Windows使用where命令
            result = subprocess.run(['where', 'chrome'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and result.stdout.strip():
                chrome_path = result.stdout.strip().split('\n')[0]
                logger.info(f"通过where命令找到Chrome: {chrome_path}")
                return chrome_path
        else:
            # Unix系统使用which命令
            for cmd in ['google-chrome-stable', 'google-chrome', 'chromium-browser', 'chromium']:
                result = subprocess.run(['which', cmd], capture_output=True, text=True, timeout=5)
                if result.returncode == 0 and result.stdout.strip():
                    chrome_path = result.stdout.strip()
                    logger.info(f"通过which命令找到Chrome: {chrome_path}")
                    return chrome_path
    except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
        logger.debug(f"使用命令查找Chrome失败: {e}")

    logger.warning("未找到Chrome浏览器")
    return None

def get_project_node_executable():
    """
    获取项目目录下的Node.js可执行文件路径

    Returns:
        str: Node.js可执行文件路径，如果未找到则返回None
    """
    try:
        # 获取项目根目录
        project_root = get_project_root()

        # 根据操作系统确定Node.js目录和可执行文件名
        system = platform.system().lower()

        if system == 'windows':
            # Windows系统
            node_dir = os.path.join(project_root, 'node-v22.17.0-win-x64')
            node_executable = os.path.join(node_dir, 'node.exe')
        elif system == 'darwin':  # macOS
            # macOS系统 - 假设有对应的macOS版本目录
            node_dir = os.path.join(project_root, 'node-v22.17.0-darwin-x64')
            node_executable = os.path.join(node_dir, 'bin', 'node')

            # 在macOS上确保可执行文件有正确的权限
            if os.path.exists(node_executable):
                try:
                    # 检查并设置执行权限
                    import stat
                    current_permissions = os.stat(node_executable).st_mode
                    if not (current_permissions & stat.S_IXUSR):
                        logger.info(f"为macOS Node.js可执行文件添加执行权限: {node_executable}")
                        os.chmod(node_executable, current_permissions | stat.S_IXUSR | stat.S_IXGRP | stat.S_IXOTH)
                except Exception as perm_error:
                    logger.warning(f"设置Node.js执行权限时出错: {perm_error}")
        else:  # Linux
            # Linux系统 - 假设有对应的Linux版本目录
            node_dir = os.path.join(project_root, 'node-v22.17.0-linux-x64')
            node_executable = os.path.join(node_dir, 'bin', 'node')

        # 检查可执行文件是否存在
        if os.path.exists(node_executable):
            logger.info(f"找到项目Node.js可执行文件: {node_executable}")
            return node_executable
        else:
            logger.warning(f"项目Node.js可执行文件不存在: {node_executable}")
            return None

    except Exception as e:
        logger.error(f"获取项目Node.js可执行文件时出错: {e}")
        return None

def check_node_dependencies():
    """
    检查Node.js依赖是否已安装

    Returns:
        bool: 依赖是否完整
    """
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        node_modules_path = os.path.join(current_dir, 'node_modules')
        package_json_path = os.path.join(current_dir, 'package.json')

        if not os.path.exists(package_json_path):
            logger.error("未找到package.json文件")
            return False

        if not os.path.exists(node_modules_path):
            logger.warning("未找到node_modules目录，需要运行npm install")
            return False

        # 检查关键依赖
        required_modules = ['whatsapp-web.js', 'qrcode-terminal']
        for module in required_modules:
            module_path = os.path.join(node_modules_path, module)
            if not os.path.exists(module_path):
                logger.warning(f"缺少依赖模块: {module}")
                return False

        logger.info("Node.js依赖检查通过")
        return True
    except Exception as e:
        logger.error(f"检查Node.js依赖时出错: {e}")
        return False

class WhatsAppService(ChatInterface):
    """WhatsApp聊天客服服务类"""
    
    def __init__(self, store_name: str, username: str = None, aggregation_timeout: int = 10):
        """
        初始化WhatsApp客服服务
        
        Args:
            store_name: 店铺名称
            aggregation_timeout: 消息聚合超时时间（秒），默认10秒
        """
        # 初始化数据库操作对象，确保在调用父类初始化前设置
        self.whatsapp_process = None
        self.store_operations = StoreOperations()
        self.context_operations = ContextOperations()
        self.intent_operations = IntentOperations()
        
        # 存储用户名，用于生成唯一的存储目录
        self.username = username
        logger.info(f"初始化WhatsApp服务，店铺: {store_name}, 用户名: {username}")
        
        # 调用父类初始化方法，会触发_platform_initialize
        super().__init__(store_name, aggregation_timeout)
    
    def _platform_initialize(self) -> None:
        """
        平台特定的初始化操作
        """
        # 获取当前目录
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        # Node.js脚本路径
        self.node_script_path = os.path.join(self.current_dir, "index.js")
        
        # 创建店铺特定的数据目录
        self.store_data_dir = os.path.join(self.current_dir, "store_data", self._get_safe_store_name())
        os.makedirs(self.store_data_dir, exist_ok=True)
        
        # 获取店铺信息
        self.store_info = self.store_operations.get_store_by_name(self.store_name)
        if not self.store_info:
            logger.error(f"未找到店铺: {self.store_name}")
            
    def _get_safe_store_name(self) -> str:
        """获取安全的店铺名称（用于文件名），使用用户名+店铺名作为唯一索引"""
        # 如果没有用户名，尝试从数据库获取
        username = self.username
        if not username:
            try:
                from database.user_operations import UserOperations
                user_ops = UserOperations()
                username = user_ops.get_current_logged_user()
                if username:
                    logger.info(f"从数据库获取到用户名: {username}")
                    # 更新实例的用户名
                    self.username = username
                else:
                    username = 'default_user'
                    logger.warning("无法从数据库获取用户名，使用默认值")
            except Exception as e:
                logger.error(f"获取用户名时出错: {str(e)}")
                username = 'default_user'

        # 清理用户名和店铺名，去除非法字符
        safe_username = "".join([c for c in username if c.isalnum() or c in "_-"])
        safe_storename = "".join([c for c in self.store_name if c.isalnum() or c in "_-"])

        # 确保安全名称不为空
        if not safe_username:
            safe_username = f"user_{hash(username) % 10000}"
        if not safe_storename:
            safe_storename = f"store_{hash(self.store_name) % 10000}"

        # 组合用户名和店铺名作为唯一索引
        combined_name = f"{safe_username}_{safe_storename}"
        logger.info(f"生成存储目录名称: {combined_name} (用户: {username}, 店铺: {self.store_name})")

        return combined_name
    
    def start(self) -> bool:
        """
        启动WhatsApp客服服务
        
        Returns:
            bool: 启动是否成功
        """
        try:
            if self.is_running:
                logger.warning("WhatsApp客服服务已在运行")
                return True
                
            logger.info(f"正在启动WhatsApp客服服务，店铺: {self.store_name}")
            
            # 创建店铺特定的进程间通信文件
            ipc_file = os.path.join(self.store_data_dir, "whatsapp_messages.json")
            
            # 清空旧的消息通信文件
            with open(ipc_file, "w", encoding="utf-8") as f:
                json.dump([], f)
            
            # 确保Node.js脚本和依赖存在
            if not os.path.exists(self.node_script_path):
                logger.error(f"未找到WhatsApp服务脚本: {self.node_script_path}")
                return False
            
            # 检查Node.js依赖
            if not check_node_dependencies():
                logger.error("Node.js依赖不完整，请运行 'npm install' 安装依赖")
                return False

            # 设置环境变量传递店铺信息
            env = os.environ.copy()
            env["STORE_NAME"] = self.store_name
            env["IPC_FILE"] = ipc_file
            env["STORE_DATA_DIR"] = self.store_data_dir

            # 根据操作系统和环境决定Chrome使用策略
            chrome_path = find_chrome_executable()
            system = platform.system().lower()

            # 在macOS下，如果找到系统Chrome，优先使用系统Chrome
            # 因为macOS下Puppeteer的Chromium下载可能有问题
            if system == 'darwin' and chrome_path:
                env["PUPPETEER_EXECUTABLE_PATH"] = chrome_path
                logger.info(f"macOS环境，使用系统Chrome路径: {chrome_path}")
            elif chrome_path and os.environ.get("USE_SYSTEM_CHROME", "false").lower() == "true":
                env["PUPPETEER_EXECUTABLE_PATH"] = chrome_path
                logger.info(f"使用系统Chrome路径: {chrome_path}")
            else:
                logger.info("使用Puppeteer内置Chromium，确保跨平台兼容性")
                # 不设置PUPPETEER_EXECUTABLE_PATH，让Puppeteer使用内置Chromium

                # 在macOS下如果没有找到Chrome，给出警告
                if system == 'darwin':
                    logger.warning("macOS环境下未找到系统Chrome，将尝试使用Puppeteer内置Chromium")
                    logger.warning("如果启动失败，请安装Chrome浏览器或运行: cd chat/whatsapp && npm install")

            # 获取项目Node.js可执行文件路径
            project_node_executable = get_project_node_executable()

            if project_node_executable:
                # 使用项目目录下的Node.js
                node_executable = project_node_executable
                logger.info(f"使用项目Node.js: {node_executable}")

                # 在macOS上，项目Node.js可能会有弹窗和性能问题的说明
                if sys.platform == 'darwin':
                    logger.info("macOS环境下使用项目Node.js可能会有以下影响：")
                    logger.info("1. 首次运行可能出现安全弹窗，需要在系统偏好设置中允许")
                    logger.info("2. 相比系统Node.js可能有轻微的性能差异")
                    logger.info("3. 如果遇到问题，可以考虑使用系统Node.js (brew install node)")
            else:
                # 回退到系统Node.js
                node_executable = 'node'
                logger.warning("项目Node.js不可用，回退到系统Node.js")

                # 在macOS上说明系统Node.js的优势
                if sys.platform == 'darwin':
                    logger.info("使用系统Node.js的优势：")
                    logger.info("1. 无安全弹窗，启动更快")
                    logger.info("2. 系统优化更好，性能更流畅")
                    logger.info("3. 自动更新和安全补丁")

            # 添加Node.js版本检查
            try:
                node_version_result = subprocess.run([node_executable, '--version'],
                                                   capture_output=True, text=True, timeout=10)
                if node_version_result.returncode == 0:
                    node_version = node_version_result.stdout.strip()
                    logger.info(f"Node.js版本: {node_version}")
                else:
                    logger.error("无法获取Node.js版本，请确保Node.js已正确安装")
                    return False
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                logger.error(f"Node.js检查失败: {e}")
                logger.error("请确保Node.js已安装并在PATH中")
                return False

            # 直接使用Node.js子进程执行JavaScript代码
            try:
                # 确保当前工作目录正确
                os.chdir(self.current_dir)

                # 构建命令行，使用确定的Node.js可执行文件
                node_cmd = [node_executable, self.node_script_path]

                # 根据操作系统设置进程创建参数
                popen_kwargs = {
                    'env': env,
                    'stdout': subprocess.PIPE,
                    'stderr': subprocess.PIPE,
                    'text': False,  # 设置为False以获取原始字节
                    'cwd': self.current_dir  # 确保工作目录正确
                }

                if sys.platform == 'win32':
                    # Windows环境：使用CREATE_NO_WINDOW标志隐藏控制台窗口
                    try:
                        # 尝试导入Windows特定API
                        import win32process
                        popen_kwargs['creationflags'] = win32process.CREATE_NO_WINDOW
                    except ImportError:
                        # 如果没有pywin32，使用数值常量
                        popen_kwargs['creationflags'] = 0x08000000  # CREATE_NO_WINDOW
                elif sys.platform == 'darwin':  # macOS
                    # macOS环境：优化进程启动，避免弹窗和卡顿
                    def macos_preexec():
                        """macOS专用的进程预执行函数，优化性能"""
                        try:
                            # 创建新的进程组，避免继承父进程的某些属性
                            os.setsid()
                            # 设置较低的进程优先级，减少系统负载和卡顿
                            import resource
                            # 设置nice值为5，降低优先级（范围-20到19，数值越高优先级越低）
                            os.nice(5)
                            # 限制进程的CPU使用时间，避免长时间占用
                            resource.setrlimit(resource.RLIMIT_CPU, (300, 600))  # 软限制5分钟，硬限制10分钟
                        except Exception as e:
                            # 如果设置失败，不影响进程启动
                            pass

                    popen_kwargs['preexec_fn'] = macos_preexec
                    # 在macOS上使用更保守的缓冲设置
                    popen_kwargs['bufsize'] = 0  # 无缓冲，减少延迟
                else:  # Linux
                    # Linux环境：使用默认设置
                    popen_kwargs['preexec_fn'] = os.setsid  # 创建新的进程组

                # 启动Node.js进程
                try:
                    logger.info(f"启动Node.js进程: {' '.join(node_cmd)}")
                    self.whatsapp_process = subprocess.Popen(node_cmd, **popen_kwargs)
                except FileNotFoundError as e:
                    logger.error(f"启动Node.js进程失败: {e}")
                    logger.error("请确保Node.js已安装并在PATH中")
                    return False
                except Exception as e:
                    logger.error(f"启动Node.js进程时出现未知错误: {e}")
                    return False
                
                logger.info(f"已启动Node.js进程，PID: {self.whatsapp_process.pid}")
                
                # 创建线程读取输出
                def read_output():
                    while True:
                        try:
                            line = self.whatsapp_process.stdout.readline()
                            if not line and self.whatsapp_process.poll() is not None:
                                break
                            if line:
                                # 使用UTF-8解码输出
                                decoded_line = line.decode('utf-8', errors='replace').strip()
                                logger.info(f"WhatsApp进程输出: {decoded_line}")
                        except Exception as e:
                            # 过滤掉服务关闭后的正常错误
                            error_msg = str(e)
                            if "'NoneType' object has no attribute 'stdout'" in error_msg:
                                # 这是服务关闭后的正常情况，不需要记录错误
                                logger.debug(f"WhatsApp进程已关闭: {error_msg}")
                            else:
                                # 记录真正的错误
                                logger.error(f"读取WhatsApp进程输出错误: {error_msg}")
                
                # 创建线程读取错误输出
                def read_error():
                    while True:
                        try:
                            line = self.whatsapp_process.stderr.readline()
                            if not line and self.whatsapp_process.poll() is not None:
                                break
                            if line:
                                # 使用UTF-8解码错误输出
                                decoded_line = line.decode('utf-8', errors='replace').strip()

                                # 过滤掉已知的非致命错误（消息实际已发送成功）
                                if any(keyword in decoded_line.lower() for keyword in [
                                    'serialize', 'getmessagemodel', 'cannot read properties of undefined',
                                    'evaluation failed: typeerror: cannot read properties of undefined',
                                    'pptr://__puppeteer_evaluation_script__', 'at pptr://'
                                ]):
                                    # 这些是WhatsApp Web.js库的内部错误，消息通常已经发送成功
                                    logger.debug(f"WhatsApp内部库信息: {decoded_line}")
                                else:
                                    # 记录真正的错误
                                    logger.error(f"WhatsApp进程错误: {decoded_line}")
                        except Exception as e:
                            # 过滤掉服务关闭后的正常错误
                            error_msg = str(e)
                            if "'NoneType' object has no attribute 'stderr'" in error_msg:
                                # 这是服务关闭后的正常情况，不需要记录错误
                                logger.debug(f"WhatsApp进程已关闭: {error_msg}")
                            else:
                                # 记录真正的错误
                                logger.error(f"读取WhatsApp进程错误输出错误: {error_msg}")
                
                # 启动输出读取线程
                stdout_thread = threading.Thread(target=read_output)
                stdout_thread.daemon = True
                stdout_thread.start()
                
                stderr_thread = threading.Thread(target=read_error)
                stderr_thread.daemon = True
                stderr_thread.start()
                
            except Exception as e:
                logger.error(f"启动Node.js进程时出错: {str(e)}")
                return False
            
            # 启动消息监控线程
            self.is_running = True
            self._start_message_monitor(ipc_file)
            
            logger.info(f"WhatsApp客服服务已启动，店铺: {self.store_name}")
            return True
        except Exception as e:
            logger.exception(f"启动WhatsApp客服服务时出错: {str(e)}")
            self.stop()
            return False
    
    def stop(self) -> bool:
        """
        停止WhatsApp客服服务
        
        Returns:
            bool: 停止是否成功
        """
        try:
            # 处理subprocess.Popen进程
            if self.whatsapp_process and self.whatsapp_process.poll() is None:  # poll()返回None表示进程仍在运行
                # 标记服务为停止状态
                self.is_running = False
                
                # 终止进程
                logger.info(f"正在终止WhatsApp进程，PID: {self.whatsapp_process.pid}")
                self.whatsapp_process.terminate()
                
                # 给进程一些时间来自行退出
                try:
                    self.whatsapp_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # 如果超时，强制结束进程
                    logger.warning("WhatsApp进程未能在5秒内终止，强制结束进程")
                    self.whatsapp_process.kill()
                
                self.whatsapp_process = None
            
            # 取消所有计时器
            # 使用统一锁管理器或后备方案
            if LOCK_MANAGER_AVAILABLE:
                lock_context = with_lock('message_aggregation')
            else:
                lock_context = self.message_lock

            with lock_context:
                for sender_id, timer in list(self.aggregation_timers.items()):
                    if timer and timer.is_alive():
                        # 无法直接停止线程，但可以移除引用
                        self.aggregation_timers[sender_id] = None

                # 清空消息缓冲
                self.message_buffer.clear()
                self.last_message_time.clear()
            
            self.is_running = False
            logger.info("WhatsApp客服服务已停止")
            return True
        except Exception as e:
            logger.exception(f"停止WhatsApp客服服务时出错: {str(e)}")
            return False
    
    def _start_message_monitor(self, ipc_file: str) -> None:
        """
        启动消息监控线程
        
        Args:
            ipc_file: 进程间通信文件路径
        """
        def monitor():
            processed_message_ids = set()  # 跟踪已处理的消息ID

            while self.is_running:
                try:
                    # 检查文件是否存在且有内容
                    if os.path.exists(ipc_file) and os.path.getsize(ipc_file) > 0:
                        # 使用文件锁安全读取
                        from .file_lock_manager import safe_read_json
                        messages = safe_read_json(ipc_file, timeout=2.0)

                        if messages and isinstance(messages, list):
                            # 过滤出未处理的新消息
                            new_messages = []
                            messages_to_update = []

                            for message in messages:
                                message_id = message.get("id")
                                if not message_id:
                                    continue

                                # 检查是否已处理过
                                if message_id not in processed_message_ids and not message.get("processed", False):
                                    new_messages.append(message)
                                    processed_message_ids.add(message_id)

                                    # 标记消息为已处理
                                    message["processed"] = True
                                    messages_to_update.append(message)

                            # 如果有新消息，处理它们
                            if new_messages:
                                logger.info(f"发现 {len(new_messages)} 条新消息待处理")

                                # 更新文件中的处理状态
                                if messages_to_update:
                                    from .file_lock_manager import safe_write_json
                                    safe_write_json(ipc_file, messages, timeout=2.0)

                                # 处理每条新消息
                                for message in new_messages:
                                    try:
                                        # 将消息加入缓冲区
                                        self.buffer_message(message)
                                    except Exception as msg_error:
                                        logger.error(f"处理消息 {message.get('id', 'unknown')} 时出错: {str(msg_error)}")

                            # 清理过期的消息ID（保留最近1000条）
                            if len(processed_message_ids) > 1000:
                                # 保留最近500条ID
                                recent_ids = list(processed_message_ids)[-500:]
                                processed_message_ids.clear()
                                processed_message_ids.update(recent_ids)
                                logger.debug("已清理过期的消息ID")

                    # 暂停一秒再检查
                    time.sleep(1)
                except Exception as e:
                    logger.error(f"监控消息时出错: {str(e)}")
                    time.sleep(5)  # 出错后等待稍长时间再重试
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor)
        monitor_thread.daemon = True
        monitor_thread.start()
        logger.info("已启动消息监控线程")
    
    def _get_sender_id(self, message: Dict[str, Any]) -> str:
        """
        从消息中提取发送者ID
        
        Args:
            message: 平台特定的消息对象
            
        Returns:
            str: 发送者ID
        """
        return message.get("from", "")
    
    def _get_message_content(self, message: Dict[str, Any]) -> str:
        """
        从消息中提取内容
        
        Args:
            message: 平台特定的消息对象
            
        Returns:
            str: 消息内容
        """
        return message.get("body", "")
    
    def _combine_messages(self, messages: List[Dict[str, Any]]) -> str:
        """
        合并多条消息内容

        Args:
            messages: 消息列表

        Returns:
            str: 合并后的消息内容
        """
        return "".join([msg.get("body", "") for msg in messages])

    def _get_aggregated_message_type(self, messages: List[Dict[str, Any]]) -> str:
        """
        获取聚合消息的类型，如果包含非文本消息则返回非文本类型

        Args:
            messages: 消息列表

        Returns:
            str: 消息类型
        """
        # 检查是否有非文本消息
        for msg in messages:
            msg_type = msg.get("type", "chat")
            if msg_type != "chat":
                return msg_type  # 返回第一个非文本消息类型
        return "chat"  # 如果都是文本消息，返回chat
    
    def _create_aggregated_message(self, sender_id: str, content: str, messages: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        创建聚合后的消息对象

        Args:
            sender_id: 发送者ID
            content: 合并后的消息内容
            messages: 原始消息列表，用于确定消息类型（可选）

        Returns:
            Dict[str, Any]: 聚合后的消息对象
        """
        # 确定消息类型
        message_type = "chat"
        if messages:
            message_type = self._get_aggregated_message_type(messages)

        return {
            "from": sender_id,
            "body": content,
            "type": message_type
        }
    
    def _get_media_handling_mode(self) -> str:
        """获取媒体消息处理模式"""
        try:
            # 从数据库读取当前用户的AI回复设置
            from database.user_operations import UserOperations
            user_ops = UserOperations()
            current_username = user_ops.get_current_logged_user()

            if not current_username:
                logger.warning("无法获取当前用户名，使用默认设置")
                return 'manual'  # 默认为人工处理

            # 从数据库获取用户的AI回复设置
            ai_enabled = user_ops.get_ai_reply_enabled(current_username)
            mode = 'auto' if ai_enabled else 'manual'

            logger.info(f"用户 {current_username} 的媒体处理模式: {mode} (ai_reply_enabled={ai_enabled})")
            return mode

        except Exception as e:
            logger.error(f"获取媒体处理模式时出错: {e}")
            return 'manual'  # 默认为人工处理

    def handle_platform_event(self, event: Any) -> None:
        """
        处理平台特定的事件

        Args:
            event: 平台特定的事件对象
        """
        # WhatsApp中暂无特定事件处理逻辑
        _ = event  # 避免未使用参数警告
        pass
    
    def process_message(self, message: Dict[str, Any]) -> None:
        """
        处理接收到的WhatsApp消息
        
        Args:
            message: 消息字典，包含from（发送者ID）和body（消息内容）
        """
        try:
            sender_id = message.get("from")
            message_body = message.get("body", "")
            message_type = message.get("type", "chat")

            if not sender_id or not message_body:
                logger.warning("收到无效消息")
                return

            logger.info(f"处理来自 {sender_id} 的消息: {message_body} (类型: {message_type})")

            # 检查消息类型，如果不是文本消息，根据配置决定处理方式
            if message_type != "chat":
                # 检查非文本消息处理模式
                media_mode = self._get_media_handling_mode()
                logger.info(f"非文本消息处理模式检查: MEDIA_HANDLING_MODE={media_mode}")

                if media_mode == 'manual':
                    logger.info(f"收到非文本消息类型 {message_type}，根据配置转为人工介入状态")

                    # 获取当前登录用户名
                    from database.user_operations import UserOperations
                    user_ops = UserOperations()
                    current_username = user_ops.get_current_logged_user() or "default_user"

                    # 获取当前上下文
                    context_record = self.context_operations.get_context(
                        current_username, self.store_name, sender_id
                    )
                    context = context_record.get("context", "") if context_record else ""
                    context_chz = context_record.get("context_trans", "") if context_record else ""

                    # 翻译消息内容（虽然是替换后的文本，但保持一致性）
                    message_body_chz = input_trans(message_body)

                    # 更新上下文（包含替换后的消息内容）
                    save_message = f"{context}\n客户的历史提问：{message_body}\n" if context else f"客户的历史提问：{message_body}\n"
                    save_message_chz = f"{context_chz}\n客户的历史提问（中）：{message_body_chz}\n" if context_chz else f"客户的历史提问（中）：{message_body_chz}\n"

                    context_id = self.context_operations.add_context(
                        current_username, self.store_name, sender_id, save_message, save_message_chz
                    )
                    logger.info(f"已更新上下文记录(ID:{context_id})，包含媒体消息提示")

                    # 创建人工介入的意图分析结果
                    intent_result = {
                        "stage": "媒体消息处理",
                        "specific_intent": f"{message_type}类型消息",
                        "intent_strength": "Strong",
                        "source": "media-detection",
                        "reasoning": f"检测到{message_type}类型的媒体消息，需要人工处理"
                    }

                    # 保存意图分析结果，设置manual=1表示需要人工介入
                    self.intent_operations.save_intent_analysis(
                        self.store_name,
                        sender_id,
                        intent_result,
                        message_body,
                        context,
                        1,  # manual=1，强制人工介入
                        current_username
                    )

                    logger.info(f"已将用户 {sender_id} 的 {message_type} 类型消息转为人工介入状态")
                    return
                else:
                    logger.info(f"收到非文本消息类型 {message_type}，根据配置继续AI处理")
                    # 继续正常的AI处理流程，不返回，让消息按照正常流程处理

            # 添加店铺状态检查
            # 首先获取最新的店铺信息，确保拍照到当前状态
            latest_store_info = self.store_operations.get_store_by_name(self.store_name)
            if latest_store_info and latest_store_info.get('plg_status', 1) == 0:
                logger.warning(f"店铺{self.store_name}状态已关闭(plg_status=0)，拒绝处理消息")
                # 更新当前的店铺信息引用
                self.store_info = latest_store_info
                # 调用弹窗提示
                handle_store_status_exception(self.store_name)
                return
            
            # 1. 调用知识库搜索获取相关知识
            # knowledge_result = search_knowledge(message_body)
            
            # 2. 获取店铺提示词(prompt)
            store_prompt = self.store_info.get("plg_prompt", "")
            
            # 3. 从上下文表中获取聊天历史
            # 获取当前登录用户名，确保与意向分析查询中的user_name一致
            from database.user_operations import UserOperations
            user_ops = UserOperations()
            current_username = user_ops.get_current_logged_user() or "default_user"

            context_record = self.context_operations.get_context(
                current_username, self.store_name, sender_id
            )
            context = context_record.get("context", "") if context_record else ""
            context_chz = context_record.get("context_trans", "") if context_record else ""

            # 4. 翻译用户消息
            message_body_chz = input_trans(message_body)

            save_message = f"{context}\n客户的历史提问：{message_body}\n" if context else f"客户的历史提问：{message_body}\n"
            # 翻译版上下文 - 统一使用与Test Chat相同的格式前缀
            save_message_chz = f"{context_chz}\n客户的历史提问（中）：{message_body_chz}\n" if context else f"客户的历史提问（中）：{message_body_chz}\n"
            # 获取当前登录用户名，确保与意向分析查询中的user_name一致
            from database.user_operations import UserOperations
            user_ops = UserOperations()
            current_username = user_ops.get_current_logged_user() or "default_user"

            context_id = self.context_operations.add_context(
                current_username, self.store_name, sender_id, save_message, save_message_chz
            )
            logger.info(f"已更新上下文记录(ID:{context_id})")
            
            # 判断是否处于人工介入状态
            if check_manual_intervention(self.store_name, sender_id):
                return
            
            # 5. 进行意图分析
            intent_result = analyze_customer_intent_hybrid(message_body_chz, context)
            logger.info(f"意图分析结果: {intent_result}")

            # 6. 根据自动化处理规则决定处理方式
            stage = intent_result.get("stage", "error")
            specific_intent = intent_result.get("specific_intent", "unknown")
            confidence = intent_result.get("intent_strength", "Medium")

            # 检查是否应该忽略处理
            if automation_rules_manager.should_ignore_processing(stage, specific_intent, confidence):
                action, description = automation_rules_manager.get_processing_action(stage, specific_intent, confidence)
                logger.info(f"根据自动化规则忽略处理: {description}")
                return

            # 确定是否需要人工介入
            manual = 1 if automation_rules_manager.should_manual_intervention(stage, specific_intent, confidence) else 0

            # 记录处理决策
            action, description = automation_rules_manager.get_processing_action(stage, specific_intent, confidence)
            logger.info(f"自动化处理决策: {action} - {description}")

            # 保存意图分析结果到数据库
            self.intent_operations.save_intent_analysis(
                self.store_name,
                sender_id,
                intent_result,
                message_body,
                context,
                manual,  # manual参数，根据自动化规则决定
                self.username  # 添加username参数，表示这条数据产生的wowcker账户
            )

            if manual:
                logger.info(f"用户{sender_id}根据自动化规则需要人工介入，不处理自动回复")
                return

            response, reply_summary = generate_LLM_reply(message_body, context, store_prompt, message_chz=message_body_chz)
            response_chz = reply_summary.get("original_response", response)

            logger.info(f"千问回复: {response}")
            # 6. 更新积分 - 使用配置系统
            from config.points_manager import get_points_config_manager
            from database.app_db_manager import AppDBManager

            points_config = get_points_config_manager()

            # 每次处理消息前先获取最新的店铺信息，确保反映轮询器可能重置的积分
            self.store_info = self.store_operations.get_store_by_name(self.store_name)

            if self.store_info and "id" in self.store_info:
                # 计算AI客服回复积分
                ai_reply_points = points_config.get_ai_service_points('reply_points')
                ai_reply_points_storage = points_config.convert_points_for_storage(ai_reply_points)

                # 使用事务原子性地更新店铺积分
                result = self.store_operations.update_points_atomically(self.store_info["id"], ai_reply_points_storage)

                if result:
                    logger.info(f"店铺积分已原子更新: {self.store_name}, 新积分: {result}")

                    # AI回复积分只记录到stores表的plg_points，不记录到points表
                    # 这样避免重复计算积分
                    logger.info(f"AI回复积分已记录到stores表: {ai_reply_points} 积分，店铺: {self.store_name}")

                    # 刷新店铺信息
                    self.store_info = self.store_operations.get_store_by_name(self.store_name)
                else:
                    logger.error(f"原子更新店铺积分失败: {self.store_name}")
            
            # 7. 更新上下文 - 使用完整聚合的问题和回答作为一次完整问答
            # 如果response是列表，则将其转换为字符串
            response_str = response
            if isinstance(response, list):
                response_str = "\n".join(response)  # 使用\n分隔每个回复
            # 统一使用与Test Chat相同的格式前缀，确保View Chat页面能正确解析
            new_context = f"{context}\n客户的历史提问：{message_body}\n你的历史回答：{response_str}" if context else f"客户的历史提问：{message_body}\n你的历史回答：{response_str}"
            # 翻译版上下文 - 统一使用与Test Chat相同的格式前缀
            new_context_trans = f"{context_chz}\n客户的历史提问（中）：{message_body_chz}\n你的历史回答（中）：{response_chz}" if context_chz else f"客户的历史提问（中）：{message_body_chz}\n你的历史回答（中）：{response_chz}"
            # 获取当前登录用户名，确保与意向分析查询中的user_name一致
            from database.user_operations import UserOperations
            user_ops = UserOperations()
            current_username = user_ops.get_current_logged_user() or "default_user"

            context_id = self.context_operations.add_context(
                current_username, self.store_name, sender_id, new_context, new_context_trans
            )
            logger.info(f"已更新上下文记录(ID:{context_id})")
            
            # 8. 发送回复并记录
            self.send_reply(sender_id, response)
            
            # 记录最后发送的消息
            # 使用统一锁管理器或后备方案
            if LOCK_MANAGER_AVAILABLE:
                lock_context = with_lock('message_aggregation')
            else:
                lock_context = self.message_lock

            with lock_context:
                self.last_sent_messages[sender_id] = response
            
        except Exception as e:
            logger.exception(f"处理消息时出错: {str(e)}")
    
    def send_reply(self, recipient_id: str, message: Union[str, List[str]]) -> None:
        """
        向WhatsApp发送回复消息 - 改进版本，支持消息去重和原子操作

        Args:
            recipient_id: 接收者ID
            message: 消息内容，可以是字符串或字符串列表
        """
        try:
            import uuid
            import tempfile

            # 创建回复文件路径
            reply_file = os.path.join(self.store_data_dir, "whatsapp_replies.json")

            # 处理消息（字符串或列表）
            if isinstance(message, str):
                message_list = [message]
            elif isinstance(message, list):
                if not message:  # 空列表情况
                    return
                message_list = message
            else:
                logger.error(f"不支持的消息类型: {type(message)}")
                return

            # 为整个消息会话生成唯一的会话ID
            session_id = str(uuid.uuid4())

            # 批量创建所有消息，每条消息都有唯一ID
            reply_data = []
            for i, msg in enumerate(message_list):
                message_id = f"{session_id}_{i}"
                reply_data.append({
                    "id": message_id,  # 唯一消息ID
                    "session_id": session_id,  # 会话ID
                    "to": recipient_id,
                    "message": msg,
                    "segment_index": i,  # 消息段索引
                    "total_segments": len(message_list),  # 总段数
                    "timestamp": time.time(),
                    "status": "pending"  # 消息状态：pending, sent, failed
                })

            # 使用原子文件操作写入所有消息
            self._write_reply_file_atomic(reply_file, reply_data)

            logger.info(f"✅ 已创建 {len(message_list)} 条消息到发送队列，会话ID: {session_id}")

            # 等待JS处理完所有消息 - 根据消息数量调整等待时间
            base_wait_time = 3.0  # 基础等待时间
            additional_wait = (len(message_list) - 1) * 2.0  # 每增加一条消息增加2秒
            total_wait_time = base_wait_time + additional_wait

            logger.info(f"等待 {total_wait_time:.1f} 秒让JS处理 {len(message_list)} 条消息...")
            time.sleep(total_wait_time)

            # 验证发送结果
            self._verify_message_sending(reply_file, session_id, len(message_list))

        except Exception as e:
            logger.exception(f"发送回复时出错: {str(e)}")

    def _write_reply_file_atomic(self, reply_file: str, reply_data: list) -> None:
        """
        原子性写入回复文件，防止并发访问冲突

        Args:
            reply_file: 目标文件路径
            reply_data: 要写入的回复数据
        """
        try:
            # 导入文件锁管理器
            from .file_lock_manager import safe_write_json

            # 使用安全写入函数
            success = safe_write_json(reply_file, reply_data, timeout=10.0)

            if success:
                logger.debug(f"安全写入回复文件成功: {len(reply_data)} 条消息")
            else:
                logger.error("安全写入回复文件失败")

        except Exception as e:
            logger.error(f"写入回复文件时出错: {str(e)}")

    def _verify_message_sending(self, reply_file: str, session_id: str, expected_count: int) -> None:
        """
        验证消息发送结果

        Args:
            reply_file: 回复文件路径
            session_id: 会话ID
            expected_count: 期望的消息数量
        """
        try:
            # 导入文件锁管理器
            from .file_lock_manager import safe_read_json

            # 安全读取文件
            remaining_data = safe_read_json(reply_file, timeout=5.0)

            if remaining_data is None:
                logger.info(f"✅ 回复文件已被JS处理完毕，会话 {session_id}")
                return

            if not isinstance(remaining_data, list):
                logger.warning(f"回复文件格式异常，会话 {session_id}")
                return

            # 统计本会话剩余的消息
            session_remaining = [msg for msg in remaining_data if msg.get("session_id") == session_id]

            if not session_remaining:
                logger.info(f"✅ 会话 {session_id} 的所有 {expected_count} 条消息已成功发送")
            else:
                logger.warning(f"⚠️ 会话 {session_id} 还有 {len(session_remaining)} 条消息未发送")
                for msg in session_remaining:
                    logger.warning(f"   未发送消息段 {msg.get('segment_index', '?')}: {msg.get('message', '')[:30]}...")

        except Exception as e:
            logger.error(f"验证消息发送结果时出错: {str(e)}")

# 处理聊天测试消息的函数
def handle_test_message(store_name: str, sender_id: str, message_content: str) -> str:
    """
    处理测试聊天消息，返回回复内容
    
    Args:
        store_name: 店铺名称
        sender_id: 发送者ID（聊天ID）
        message_content: 消息内容
        
    Returns:
        str: 回复内容
    """
    # 动态获取当前登录用户名
    try:
        from database.user_operations import UserOperations
        user_ops = UserOperations()
        username = user_ops.get_current_logged_user()
        if not username:
            username = "default_user"  # 如果无法获取，使用默认值
    except Exception as e:
        logger.error(f"获取当前用户名失败: {str(e)}")
        username = "default_user"  # 出错时使用默认值

    try:
        # 初始化数据库操作
        store_operations = StoreOperations()
        context_operations = ContextOperations()
        intent_operations = IntentOperations()
        
        # 获取店铺信息
        store_info = store_operations.get_store_by_name(store_name)
        if not store_info:
            return f"找不到店铺: {store_name}"
        
        # 检查店铺状态，如果状态为0（关闭或付款逾期），则返回None
        # 这里返回None而不是错误信息，因为在测试聊天界面我们已经用弹窗显示错误
        if store_info.get('plg_status', 1) == 0:
            logger.warning(f"店铺{store_name}状态已关闭(plg_status=0)，拒绝处理消息")
            return None
        
        # 获取店铺提示词
        store_prompt = store_info.get("plg_prompt", "")
        
        # 获取聊天历史上下文
        context_record = context_operations.get_context(
            username, store_name, sender_id
        )
        context = context_record.get("context", "") if context_record else ""
        context_chz = context_record.get("context_trans", "") if context_record else ""

        # 4. 翻译用户消息
        message_content_chz = input_trans(message_content)

        # 统一使用与WhatsApp消息相同的格式前缀，确保View Chat页面能正确解析
        new_context = f"{context}\n客户的历史提问：{message_content}\n" if context else f"客户的历史提问：{message_content}\n"
        # 翻译版上下文 - 统一使用相同的格式前缀
        new_context_trans = f"{context_chz}\n客户的历史提问（中）：{message_content_chz}\n" if context_chz else f"客户的历史提问（中）：{message_content_chz}\n"
        context_id = context_operations.add_context(
            username, store_name, sender_id, new_context, new_context_trans
        )
        logger.info(f"已更新上下文记录(ID:{context_id})")
        
        # 判断是否处于人工介入状态
        if check_manual_intervention(store_name, sender_id):
            return

        # 5. 进行意图分析
        intent_result = analyze_customer_intent_hybrid(message_content_chz, context)
        logger.info(f"意图分析结果: {intent_result}")

        # 6. 根据自动化处理规则决定处理方式
        stage = intent_result.get("stage", "error")
        specific_intent = intent_result.get("specific_intent", "unknown")
        confidence = intent_result.get("intent_strength", "Medium")

        # 确定是否需要人工介入
        manual = 1 if automation_rules_manager.should_manual_intervention(stage, specific_intent, confidence) else 0

        # 记录处理决策
        action, description = automation_rules_manager.get_processing_action(stage, specific_intent, confidence)
        logger.info(f"测试聊天自动化处理决策: {action} - {description}")

        # 7. 保存意图分析结果到数据库
        intent_operations.save_intent_analysis(
            store_name,
            sender_id,
            intent_result,
            message_content,
            context,
            manual,  # manual参数，根据自动化规则决定
            username  # 添加username参数，表示这条数据产生的wowcker账户
        )

        if manual:
            logger.info(f"测试聊天用户{sender_id}根据自动化规则需要人工介入，不处理自动回复")
            return
        
        # 检查是否应该忽略处理
        if automation_rules_manager.should_ignore_processing(stage, specific_intent, confidence):
            action, description = automation_rules_manager.get_processing_action(stage, specific_intent, confidence)
            logger.info(f"测试聊天根据自动化规则忽略处理: {description}")
            return
        
        # 生成回复
        response, reply_summary = generate_LLM_reply(message_content, context, store_prompt, message_chz=message_content_chz)
        response_chz = reply_summary.get("original_response", response)
        logger.info(f"测试消息千问回复: {response}")
        
        # 统一使用与WhatsApp消息相同的格式前缀，确保View Chat页面能正确解析
        new_context = f"{context}\n客户的历史提问：{message_content}\n你的历史回答：{response}" if context else f"客户的历史提问：{message_content}\n你的历史回答：{response}"
        # 翻译版上下文 - 统一使用相同的格式前缀
        new_context_trans = f"{context_chz}\n客户的历史提问（中）：{message_content_chz}\n你的历史回答（中）：{response_chz}" if context_chz else f"客户的历史提问（中）：{message_content_chz}\n你的历史回答（中）：{response_chz}"
        context_id = context_operations.add_context(
            username, store_name, sender_id, new_context, new_context_trans
        )
        
        # 更新店铺积分 - 使用配置系统
        if store_info and "id" in store_info:
            from config.points_manager import get_points_config_manager
            from database.app_db_manager import AppDBManager

            points_config = get_points_config_manager()

            # 计算AI客服回复积分
            ai_reply_points = points_config.get_ai_service_points('reply_points')
            ai_reply_points_storage = points_config.convert_points_for_storage(ai_reply_points)

            # 使用事务原子性地更新积分
            result = store_operations.update_points_atomically(store_info["id"], ai_reply_points_storage)

            if result:
                logger.info(f"测试聊天店铺积分已原子更新: {store_name}, 新积分: {result}")

                # AI回复积分只记录到stores表的plg_points，不记录到points表
                # 这样避免重复计算积分
                logger.info(f"测试聊天AI回复积分已记录到stores表: {ai_reply_points} 积分，店铺: {store_name}")
            else:
                logger.warning(f"测试聊天店铺积分更新失败: {store_name}")
        
        # 返回生成的回复
        if isinstance(response, list):
            return "\n".join(response)  # 如果是列表，转换为字符串
        return response
        
    except Exception as e:
        logger.exception(f"处理测试消息时出错: {str(e)}")
        return f"处理消息失败: {str(e)}"


# 检查人工介入模式
def check_manual_intervention(store_name: str, sender_id: str) -> bool:
    """
    检查用户是否处于人工介入模式
    
    Args:
        store_name: 店铺名称
        sender_id: 发送者ID
        
    Returns:
        bool: True表示处于人工介入模式，False表示非人工介入模式
    """
    from database.intent_operations import IntentOperations
    import logging
    
    logger = logging.getLogger(__name__)
    intent_operations = IntentOperations()
    
    try:
        # 检查数据库中是否存在对应的intent_analysis记录
        conn = intent_operations.db_manager.connect()
        if conn:
            query = """
            SELECT manual FROM intent_analysis 
            WHERE store_name = ? AND sender_id = ? 
            ORDER BY created_at DESC LIMIT 1
            """
            intent_operations.db_manager.cursor.execute(query, (store_name, sender_id))
            record = intent_operations.db_manager.cursor.fetchone()
            
            # 如果记录存在且manual值为1，则处于人工介入模式
            if record and record[0] == 1:
                logger.info(f"用户 {sender_id} 处于人工介入模式，不处理自动回复")
                intent_operations.db_manager.close()
                return True
            
            intent_operations.db_manager.close()
            return False
    except Exception as e:
        logger.error(f"检查人工介入状态时出错: {str(e)}")
        # 出错时默认为非人工介入模式
        return False


# 测试代码
if __name__ == "__main__":
    # 获取当前用户名用于测试
    try:
        from database.user_operations import UserOperations
        user_ops = UserOperations()
        username = user_ops.get_current_logged_user() or "test_user"
    except:
        username = "test_user"

    service = WhatsAppService("测试店铺", username=username)
    service.start()
    
    try:
        # 保持运行直到手动中断
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        service.stop()
