# Instagram评论获客功能

## 功能概述

Instagram评论获客功能是一个自动化工具，可以帮助您：

1. **分析Instagram帖子评论** - 使用AI技术识别潜在客户
2. **自动回复目标客户** - 根据预设模板自动回复感兴趣的用户
3. **引流到WhatsApp** - 通过回复引导用户到您的主页查看WhatsApp联系方式

## 主要特性

### 🤖 智能分析
- **Qwen大模型驱动** - 使用阿里云通义千问进行智能分析
- **多层次分析** - 购买意向、积极情感、具体需求、互动意愿
- **高精度识别** - 测试准确率达到87.5%以上
- **智能回退** - Qwen不可用时自动切换到规则分析
- **垃圾评论过滤** - 自动识别和过滤无意义内容

### 📱 Instagram集成
- 安全的Instagram账户登录
- 支持2FA验证
- 会话持久化
- 批量帖子处理

### 💬 自动回复
- 多模板随机回复
- 人性化延迟设置
- 防止重复回复
- 实时状态监控

### 🛡️ 安全控制
- 智能频率控制
- 随机延迟模拟
- 异常检测和恢复
- 详细日志记录

## 使用方法

### 1. 安装依赖
```bash
pip install instagrapi PySide6
```

### 2. 配置Qwen API
确保在 `config/settings.yaml` 中配置了Qwen API：
```yaml
api:
  qwenmax:
    api_key: "your_qwen_api_key"
    endpoint: "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    model: "qwen-max"
    temperature: 0.1
    max_tokens: 500
```

### 2. 启动程序
运行主程序，在左侧菜单中选择"Ins评论获客"

### 3. 登录Instagram
1. 点击"登录Instagram"按钮
2. 输入用户名和密码
3. 如果需要，输入验证码
4. 等待登录成功

### 4. 配置参数
- **Instagram帖子URL**: 输入要分析的帖子链接，每行一个
- **目标客户意图描述**: 描述您的目标客户特征，如"对杯子很感兴趣"
- **回复话术模板**: 设置回复模板，每行一个

### 5. 分析评论
点击"分析评论"按钮，系统将：
- 获取帖子评论
- AI分析每条评论
- 识别潜在客户
- 显示分析结果

### 6. 开始自动回复
点击"开始回复"按钮，系统将：
- 自动回复目标客户
- 使用随机模板
- 添加人性化延迟
- 实时显示进度

## 配置建议

### 回复模板示例
```
感谢您的关注！请查看我的主页了解更多产品信息 📱
您好！我们有很多类似的产品，欢迎私信了解详情 ☕
谢谢喜欢！更多款式请看主页，有WhatsApp联系方式 💬
```

### 意图描述示例
```
对咖啡杯、马克杯感兴趣，想要购买或了解价格
```

### 安全设置
- 每天回复数量建议控制在20-50条
- 使用多个不同的回复模板
- 避免在深夜时段操作
- 定期更换IP地址

## 注意事项

### ⚠️ 风险提醒
1. **账号安全**: 建议使用小号测试，不要用主要业务账号
2. **频率控制**: 避免过度自动化，保持人性化操作
3. **内容合规**: 确保回复内容符合Instagram社区准则
4. **法律合规**: 遵守当地法律法规和平台条款

### 📋 最佳实践
1. **渐进式使用**: 新账号需要先养号，逐步增加活动
2. **内容质量**: 提供真实有价值的回复内容
3. **多样化操作**: 不要只进行自动回复，也要有正常的社交活动
4. **监控反馈**: 定期检查账号状态和用户反馈

## 技术架构

### 核心模块
- `instagram_client.py` - Instagram API客户端
- `ai_analyzer.py` - AI评论分析器
- `comment_manager.py` - 评论管理器
- `instagram_comment_controller.py` - GUI控制器

### 依赖库
- `instagrapi` - Instagram私有API
- `PySide6` - GUI框架
- `threading` - 多线程支持

## 故障排除

### 常见问题

**Q: 登录失败怎么办？**
A: 检查用户名密码是否正确，如果启用了2FA请输入验证码

**Q: 无法获取评论？**
A: 确保帖子URL正确，账号有权限访问该帖子

**Q: AI分析不准确？**
A: 调整意图描述和关键词，使其更具体和准确

**Q: 回复发送失败？**
A: 检查网络连接，可能是频率限制，稍后再试

### 错误代码
- `LoginRequired` - 需要重新登录
- `PleaseWaitFewMinutes` - 请求过于频繁
- `ChallengeRequired` - 需要验证身份

## 更新日志

### v1.0.0 (当前版本)
- ✅ 基础Instagram集成
- ✅ AI评论分析
- ✅ 自动回复功能
- ✅ GUI界面
- ✅ 安全控制机制

### 计划功能
- 🔄 更高级的AI模型集成
- 🔄 多账户管理
- 🔄 数据统计和报告
- 🔄 更多社交平台支持

## 支持

如有问题或建议，请：
1. 查看本文档和FAQ
2. 检查日志文件
3. 联系技术支持

---

**免责声明**: 本工具仅供学习和研究使用。使用者需自行承担使用风险，并遵守相关平台的使用条款。
