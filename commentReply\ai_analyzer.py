# -*- coding: utf-8 -*-
"""
AI评论分析器
用于判断评论是否符合目标客户画像
使用Qwen大模型进行智能分析
"""

import re
import json
import logging
import sys
import os
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入Qwen API
try:
    from model.Qwen.qwen_api import get_qwen_response
    QWEN_AVAILABLE = True
except ImportError:
    QWEN_AVAILABLE = False
    logging.warning("Qwen API不可用，将使用基础规则分析")

class AICommentAnalyzer:
    """AI评论分析器"""
    
    def __init__(self):
        self.intent_keywords = []
        self.negative_keywords = ['spam', 'fake', 'scam', 'bot', '垃圾', '假的', '骗子']
        
    def set_intent_keywords(self, keywords: List[str]):
        """
        设置意图关键词
        
        Args:
            keywords: 关键词列表
        """
        self.intent_keywords = [kw.lower().strip() for kw in keywords if kw.strip()]
        logging.info(f"设置意图关键词: {self.intent_keywords}")
    
    def analyze_comment(self, comment_text: str, intent_description: str = "") -> Dict[str, Any]:
        """
        分析评论是否符合目标客户画像
        使用Qwen大模型进行智能分析

        Args:
            comment_text: 评论内容
            intent_description: 意图描述

        Returns:
            Dict[str, Any]: 分析结果
        """
        if not comment_text or not comment_text.strip():
            return {
                'is_target_customer': False,
                'confidence': 0.0,
                'reason': '评论内容为空',
                'keywords_found': [],
                'analysis_time': datetime.now()
            }

        # 如果Qwen可用，使用AI分析
        if QWEN_AVAILABLE:
            return self._analyze_with_qwen(comment_text, intent_description)
        else:
            # 回退到基础规则分析
            return self._analyze_with_rules(comment_text, intent_description)

    def _analyze_with_qwen(self, comment_text: str, intent_description: str = "") -> Dict[str, Any]:
        """
        使用Qwen大模型分析评论
        """
        try:
            # 构建分析提示词
            analysis_prompt = self._build_analysis_prompt(comment_text, intent_description)

            # 调用Qwen API
            response = get_qwen_response(
                prompt=analysis_prompt,
                system_prompt=self._get_system_prompt(),
                temperature=0.1,  # 低温度确保结果稳定
                top_p=0.3,
                max_tokens=500
            )

            # 解析AI响应
            result = self._parse_qwen_response(response, comment_text)

            logging.info(f"Qwen分析结果: {result}")
            return result

        except Exception as e:
            logging.error(f"Qwen分析失败，回退到规则分析: {e}")
            return self._analyze_with_rules(comment_text, intent_description)

    def _build_analysis_prompt(self, comment_text: str, intent_description: str = "") -> str:
        """
        构建分析提示词
        """
        prompt_data = {
            "task": "分析Instagram评论是否为目标客户",
            "comment": comment_text,
            "target_intent": intent_description if intent_description else "对产品感兴趣的潜在客户",
            "analysis_criteria": [
                "购买意向：是否表达了购买、询价、了解产品的意图",
                "积极情感：是否对产品表现出喜爱、赞美等积极情感",
                "具体需求：是否提到具体的产品需求或使用场景",
                "互动意愿：是否希望进一步了解或联系"
            ],
            "output_format": {
                "is_target_customer": "布尔值，true或false",
                "confidence": "0-1之间的数值，表示置信度",
                "reason": "简短的分析原因",
                "keywords": "识别到的关键词列表"
            }
        }

        return json.dumps(prompt_data, ensure_ascii=False, indent=2)

    def _get_system_prompt(self) -> str:
        """
        获取系统提示词
        """
        return """你是一个专业的Instagram营销分析师，专门分析评论是否与目标客户意图匹配。

你的任务是判断一条Instagram评论是否与给定的目标客户意图在语义上匹配。

判断标准：
- 评论内容是否与目标意图在语义上相关
- 是否表达了相关的需求、兴趣或购买意向
- 忽略纯表情、垃圾评论、无意义内容

请严格按照以下JSON格式返回分析结果：
{
    "is_match": true/false,
    "reason": "匹配原因",
    "keywords": ["关键词1", "关键词2"]
}

注意：
- 只返回JSON格式，不要添加其他文字
- 语义匹配即可，不需要严格的关键词匹配
- 垃圾评论、纯表情、无意义内容应判断为false"""

    def _parse_qwen_response(self, response: str, comment_text: str) -> Dict[str, Any]:
        """
        解析Qwen的响应结果
        """
        try:
            # 清理响应文本
            cleaned_response = response.strip()

            # 尝试直接解析JSON
            if cleaned_response.startswith('{') and cleaned_response.endswith('}'):
                result = json.loads(cleaned_response)
            else:
                # 尝试提取JSON部分，支持不完整的JSON
                import re
                json_match = re.search(r'\{[^}]*\}', cleaned_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    # 尝试修复不完整的JSON
                    if not json_str.endswith('}'):
                        json_str += '}'
                    result = json.loads(json_str)
                else:
                    # 如果找不到完整JSON，尝试提取关键信息
                    result = self._extract_info_from_text(cleaned_response)

            # 验证和标准化结果
            is_match = bool(result.get('is_match', result.get('is_target_customer', False)))
            return {
                'is_target_customer': is_match,
                'confidence': 1.0 if is_match else 0.0,  # 简化为匹配/不匹配
                'reason': str(result.get('reason', 'AI分析结果')),
                'keywords_found': list(result.get('keywords', [])),
                'analysis_time': datetime.now(),
                'analysis_method': 'qwen_ai'
            }

        except Exception as e:
            logging.error(f"解析Qwen响应失败: {e}, 响应内容: {response}")
            # 回退到基础分析
            return self._analyze_with_rules(comment_text, "")

    def _extract_info_from_text(self, text: str) -> Dict[str, Any]:
        """
        从文本中提取信息（当JSON解析失败时的备用方案）
        """
        result = {
            'is_match': False,
            'is_target_customer': False,
            'reason': '文本解析结果',
            'keywords': []
        }

        # 查找关键信息
        if 'true' in text.lower():
            result['is_match'] = True
            result['is_target_customer'] = True

        # 尝试提取原因
        import re
        reason_match = re.search(r'"reason":\s*"([^"]*)"', text)
        if reason_match:
            result['reason'] = reason_match.group(1)

        return result

    def _analyze_with_rules(self, comment_text: str, intent_description: str = "") -> Dict[str, Any]:
        """
        基于规则的评论分析（备用方案）
        """
        comment_lower = comment_text.lower()

        # 检查是否包含垃圾内容
        if self._is_spam_comment(comment_lower):
            return {
                'is_target_customer': False,
                'confidence': 0.0,
                'reason': '疑似垃圾评论',
                'keywords_found': [],
                'analysis_time': datetime.now(),
                'analysis_method': 'rule_based'
            }

        # 基于关键词的简单分析
        keywords_found = []
        confidence = 0.0

        # 检查意图关键词
        for keyword in self.intent_keywords:
            if keyword in comment_lower:
                keywords_found.append(keyword)
                confidence += 0.3

        # 基于意图描述的分析
        if intent_description:
            intent_keywords = self._extract_keywords_from_intent(intent_description)
            for keyword in intent_keywords:
                if keyword in comment_lower:
                    keywords_found.append(keyword)
                    confidence += 0.2

        # 检查购买意向词汇
        purchase_indicators = [
            'buy', 'purchase', 'get', 'want', 'need', 'interested', 'price', 'cost', 'how much',
            '买', '购买', '想要', '需要', '感兴趣', '价格', '多少钱', '哪里买', '怎么买'
        ]

        for indicator in purchase_indicators:
            if indicator in comment_lower:
                keywords_found.append(indicator)
                confidence += 0.4

        # 检查积极情感词汇
        positive_words = [
            'love', 'like', 'amazing', 'beautiful', 'great', 'awesome', 'perfect',
            '喜欢', '爱', '漂亮', '好看', '棒', '完美', '太好了'
        ]

        for word in positive_words:
            if word in comment_lower:
                keywords_found.append(word)
                confidence += 0.1

        # 简化判断逻辑：有关键词就匹配
        is_target = len(keywords_found) > 0

        reason = f"基于关键词匹配: {', '.join(keywords_found[:3])}" if is_target else "未找到相关关键词"

        return {
            'is_target_customer': is_target,
            'confidence': 1.0 if is_target else 0.0,  # 简化为匹配/不匹配
            'reason': reason,
            'keywords_found': list(set(keywords_found)),  # 去重
            'analysis_time': datetime.now(),
            'analysis_method': 'rule_based'
        }
    
    def _is_spam_comment(self, comment_lower: str) -> bool:
        """检查是否为垃圾评论"""
        for keyword in self.negative_keywords:
            if keyword in comment_lower:
                return True
        
        # 检查是否全是表情符号或特殊字符
        text_chars = re.sub(r'[^\w\s]', '', comment_lower)
        if len(text_chars.strip()) < 3:
            return True
        
        # 检查是否重复字符过多
        if len(set(comment_lower)) < len(comment_lower) * 0.3:
            return True
        
        return False
    
    def _extract_keywords_from_intent(self, intent_description: str) -> List[str]:
        """从意图描述中提取关键词"""
        # 简单的关键词提取
        words = re.findall(r'\b\w+\b', intent_description.lower())
        # 过滤掉常见的停用词
        stop_words = {'的', '是', '在', '有', '和', '对', '很', '非常', 'the', 'is', 'are', 'and', 'or', 'but'}
        keywords = [word for word in words if word not in stop_words and len(word) > 1]
        return keywords
    
    def _generate_reason(self, is_target: bool, confidence: float, keywords_found: List[str]) -> str:
        """生成分析原因"""
        if not is_target:
            if confidence == 0:
                return "未发现相关关键词或购买意向"
            else:
                return f"置信度较低({confidence:.2f})，关键词: {', '.join(keywords_found[:3])}"
        else:
            return f"发现购买意向，置信度: {confidence:.2f}，关键词: {', '.join(keywords_found[:5])}"
    
    def batch_analyze_comments(self, comments: List[Dict], intent_description: str = "") -> List[Dict]:
        """
        批量分析评论
        
        Args:
            comments: 评论列表
            intent_description: 意图描述
            
        Returns:
            List[Dict]: 分析结果列表
        """
        results = []
        
        for comment in comments:
            comment_text = comment.get('text', '')
            analysis = self.analyze_comment(comment_text, intent_description)
            
            # 合并评论信息和分析结果
            result = {
                **comment,
                'analysis': analysis
            }
            results.append(result)
        
        return results
    
    def get_target_customers(self, analyzed_comments: List[Dict], min_confidence: float = 0.3) -> List[Dict]:
        """
        获取目标客户评论
        
        Args:
            analyzed_comments: 已分析的评论列表
            min_confidence: 最小置信度阈值
            
        Returns:
            List[Dict]: 目标客户评论列表
        """
        target_comments = []
        
        for comment in analyzed_comments:
            analysis = comment.get('analysis', {})
            if (analysis.get('is_target_customer', False) and 
                analysis.get('confidence', 0) >= min_confidence):
                target_comments.append(comment)
        
        # 按置信度排序
        target_comments.sort(key=lambda x: x['analysis']['confidence'], reverse=True)
        
        return target_comments
