# -*- coding: utf-8 -*-
"""
评论管理器
统一管理Instagram评论获客流程
"""

import logging
import random
import time
from typing import List, Dict, Optional, Any, Callable
from datetime import datetime
try:
    from .instagram_client import InstagramClient
    from .ai_analyzer import AICommentAnalyzer
except ImportError:
    from instagram_client import InstagramClient
    from ai_analyzer import AICommentAnalyzer

# 导入Qwen API用于话术改写
try:
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.append(project_root)
    from model.Qwen.qwen_api import get_qwen_response
    QWEN_AVAILABLE = True
except ImportError:
    QWEN_AVAILABLE = False
    logging.warning("Qwen API不可用，将使用原始话术模板")

class CommentManager:
    """评论管理器"""
    
    def __init__(self):
        self.instagram_client = InstagramClient()
        self.ai_analyzer = AICommentAnalyzer()
        self.reply_templates = []
        self.processed_comments = set()  # 已处理的评论ID
        self.is_running = False
        
        # 回调函数
        self.on_status_update: Optional[Callable[[str], None]] = None
        self.on_comment_found: Optional[Callable[[Dict], None]] = None
        self.on_reply_sent: Optional[Callable[[Dict], None]] = None
        self.on_error: Optional[Callable[[str], None]] = None
    
    def set_callbacks(self, 
                     on_status_update: Optional[Callable[[str], None]] = None,
                     on_comment_found: Optional[Callable[[Dict], None]] = None,
                     on_reply_sent: Optional[Callable[[Dict], None]] = None,
                     on_error: Optional[Callable[[str], None]] = None):
        """设置回调函数"""
        self.on_status_update = on_status_update
        self.on_comment_found = on_comment_found
        self.on_reply_sent = on_reply_sent
        self.on_error = on_error
    
    def login_instagram(self, username: str, password: str, verification_code: str = "") -> tuple[bool, str]:
        """
        登录Instagram
        
        Args:
            username: 用户名
            password: 密码
            verification_code: 验证码
            
        Returns:
            tuple[bool, str]: (是否成功, 消息)
        """
        success, message = self.instagram_client.login(username, password, verification_code)
        
        if self.on_status_update:
            status = "Instagram登录成功" if success else f"Instagram登录失败: {message}"
            self.on_status_update(status)
        
        return success, message
    
    def logout_instagram(self):
        """登出Instagram"""
        self.instagram_client.logout()
        if self.on_status_update:
            self.on_status_update("已登出Instagram")
    
    def get_login_status(self) -> Dict[str, Any]:
        """获取登录状态"""
        return self.instagram_client.get_login_status()
    
    def set_intent_keywords(self, keywords: List[str]):
        """设置意图关键词"""
        self.ai_analyzer.set_intent_keywords(keywords)
    
    def set_reply_templates(self, templates: List[str]):
        """设置回复模板"""
        self.reply_templates = [t.strip() for t in templates if t.strip()]
        logging.info(f"设置回复模板: {len(self.reply_templates)}个")

    def rewrite_reply_template(self, original_template: str, comment_text: str = "") -> str:
        """
        使用Qwen改写回复话术，避免重复

        Args:
            original_template: 原始话术模板
            comment_text: 评论内容（可选，用于个性化改写）

        Returns:
            str: 改写后的话术
        """
        if not QWEN_AVAILABLE:
            return original_template

        try:
            # 构建改写提示词
            rewrite_prompt = f"""请将以下回复话术进行改写，保持原意但换一种表达方式，使其更自然、个性化：

原始话术：{original_template}

要求：
1. 保持原意和语气
2. 换一种表达方式
3. 保持简洁友好
4. 适合Instagram评论回复
5. 直接返回改写后的话术，不要添加其他内容

改写后的话术："""

            # 调用Qwen API
            rewritten = get_qwen_response(
                prompt=rewrite_prompt,
                temperature=0.7,  # 适中的创造性
                max_tokens=200,
                top_p=0.8
            )

            # 清理响应
            rewritten = rewritten.strip()
            if rewritten and len(rewritten) > 10:  # 确保改写有效
                logging.info(f"话术改写成功: {original_template} -> {rewritten}")
                return rewritten
            else:
                logging.warning("话术改写结果无效，使用原始模板")
                return original_template

        except Exception as e:
            logging.error(f"话术改写失败: {e}")
            return original_template
    
    def analyze_post_comments(self, post_url: str, intent_description: str = "", max_comments: int = 50) -> Dict[str, Any]:
        """
        分析帖子评论

        Args:
            post_url: 帖子URL
            intent_description: 意图描述
            max_comments: 最大评论数

        Returns:
            Dict[str, Any]: 分析结果
        """
        if not self.instagram_client.is_logged_in:
            return {
                'success': False,
                'message': '请先登录Instagram',
                'comments': [],
                'target_comments': []
            }

        try:
            # 获取media_id
            if self.on_status_update:
                self.on_status_update("正在获取帖子信息...")

            logging.info(f"开始分析帖子: {post_url}")

            media_id = self.instagram_client.get_media_id_from_url(post_url)
            if not media_id:
                error_msg = '无法获取帖子信息，请检查URL是否正确'
                logging.error(f"{error_msg} - URL: {post_url}")
                return {
                    'success': False,
                    'message': error_msg,
                    'comments': [],
                    'target_comments': []
                }

            # 获取评论
            if self.on_status_update:
                self.on_status_update("正在获取评论...")

            logging.info(f"开始获取评论，media_id: {media_id}")

            comments = self.instagram_client.get_media_comments(media_id, max_comments)
            if not comments:
                error_msg = '未获取到评论，可能是私有帖子或无评论'
                logging.warning(f"{error_msg} - media_id: {media_id}")
                return {
                    'success': False,
                    'message': error_msg,
                    'comments': [],
                    'target_comments': []
                }

            logging.info(f"成功获取 {len(comments)} 条评论")

            # AI分析评论
            if self.on_status_update:
                self.on_status_update("正在分析评论...")

            analyzed_comments = self.ai_analyzer.batch_analyze_comments(comments, intent_description)
            target_comments = self.ai_analyzer.get_target_customers(analyzed_comments)

            logging.info(f"分析完成: 共{len(comments)}条评论，发现{len(target_comments)}个潜在客户")

            if self.on_status_update:
                self.on_status_update(f"分析完成: 共{len(comments)}条评论，发现{len(target_comments)}个潜在客户")

            return {
                'success': True,
                'message': f'分析完成: 共{len(comments)}条评论，发现{len(target_comments)}个潜在客户',
                'media_id': media_id,
                'comments': analyzed_comments,
                'target_comments': target_comments
            }

        except Exception as e:
            error_msg = f"分析评论失败: {str(e)}"
            logging.error(f"{error_msg} - URL: {post_url}")
            import traceback
            traceback.print_exc()

            if self.on_error:
                self.on_error(error_msg)

            return {
                'success': False,
                'message': error_msg,
                'comments': [],
                'target_comments': []
            }
    
    def analyze_and_reply(self, post_urls: List[str], intent_description: str = "", max_comments_per_post: int = 50):
        """
        分析评论并自动回复（合并功能）

        Args:
            post_urls: 帖子URL列表
            intent_description: 意图描述
            max_comments_per_post: 每个帖子最大评论数
        """
        if not self.instagram_client.is_logged_in:
            if self.on_error:
                self.on_error("请先登录Instagram")
            return

        if not self.reply_templates:
            if self.on_error:
                self.on_error("请先设置回复模板")
            return

        self.is_running = True

        try:
            for post_url in post_urls:
                if not self.is_running:
                    break

                if self.on_status_update:
                    self.on_status_update(f"正在处理帖子: {post_url}")

                # 分析帖子评论
                result = self.analyze_post_comments(post_url, intent_description, max_comments_per_post)

                if not result['success']:
                    if self.on_error:
                        self.on_error(f"处理帖子失败: {result['message']}")
                    continue

                # 回复目标客户
                target_comments = result['target_comments']
                media_id = result['media_id']

                if self.on_status_update:
                    self.on_status_update(f"找到 {len(target_comments)} 个匹配评论，开始回复...")

                for comment in target_comments:
                    if not self.is_running:
                        break

                    comment_pk = comment['pk']
                    comment_text = comment['text']

                    # 检查是否已处理过
                    if comment_pk in self.processed_comments:
                        continue

                    # 选择并改写回复模板
                    original_template = random.choice(self.reply_templates)
                    reply_text = self.rewrite_reply_template(original_template, comment_text)

                    # 发送回复
                    success, message = self.instagram_client.reply_to_comment(
                        media_id, reply_text, comment_pk
                    )

                    if success:
                        self.processed_comments.add(comment_pk)

                        if self.on_reply_sent:
                            self.on_reply_sent({
                                'comment': comment,
                                'original_template': original_template,
                                'reply_text': reply_text,
                                'timestamp': datetime.now()
                            })

                        if self.on_status_update:
                            self.on_status_update(f"已回复用户 @{comment['user']['username']}: {reply_text}")
                    else:
                        if self.on_error:
                            self.on_error(f"回复失败: {message}")

                    # 随机延迟
                    delay = random.uniform(10, 30)  # 10-30秒随机延迟
                    if self.on_status_update:
                        self.on_status_update(f"等待 {int(delay)} 秒后继续...")
                    time.sleep(delay)

                # 帖子间延迟
                if self.is_running and len(post_urls) > 1:
                    delay = random.uniform(60, 120)  # 1-2分钟随机延迟
                    if self.on_status_update:
                        self.on_status_update(f"等待{int(delay)}秒后处理下一个帖子...")
                    time.sleep(delay)

        except Exception as e:
            error_msg = f"自动回复过程中出错: {str(e)}"
            logging.error(error_msg)
            import traceback
            traceback.print_exc()
            if self.on_error:
                self.on_error(error_msg)

        finally:
            self.is_running = False
            if self.on_status_update:
                self.on_status_update("回复完成！可以继续输入新的帖子URL")

    def start_auto_reply(self, post_urls: List[str], intent_description: str = "", max_comments_per_post: int = 50):
        """
        开始自动回复
        
        Args:
            post_urls: 帖子URL列表
            intent_description: 意图描述
            max_comments_per_post: 每个帖子最大评论数
        """
        if not self.instagram_client.is_logged_in:
            if self.on_error:
                self.on_error("请先登录Instagram")
            return
        
        if not self.reply_templates:
            if self.on_error:
                self.on_error("请先设置回复模板")
            return
        
        self.is_running = True
        
        try:
            for post_url in post_urls:
                if not self.is_running:
                    break
                
                if self.on_status_update:
                    self.on_status_update(f"正在处理帖子: {post_url}")
                
                # 分析帖子评论
                result = self.analyze_post_comments(post_url, intent_description, max_comments_per_post)
                
                if not result['success']:
                    if self.on_error:
                        self.on_error(f"处理帖子失败: {result['message']}")
                    continue
                
                # 回复目标客户
                target_comments = result['target_comments']
                media_id = result['media_id']
                
                for comment in target_comments:
                    if not self.is_running:
                        break
                    
                    comment_pk = comment['pk']
                    
                    # 检查是否已处理过
                    if comment_pk in self.processed_comments:
                        continue
                    
                    # 选择回复模板
                    reply_text = random.choice(self.reply_templates)
                    
                    # 发送回复
                    success, message = self.instagram_client.reply_to_comment(
                        media_id, reply_text, comment_pk
                    )
                    
                    if success:
                        self.processed_comments.add(comment_pk)
                        
                        if self.on_reply_sent:
                            self.on_reply_sent({
                                'comment': comment,
                                'reply_text': reply_text,
                                'timestamp': datetime.now()
                            })
                        
                        if self.on_status_update:
                            self.on_status_update(f"已回复用户 @{comment['user']['username']}")
                    else:
                        if self.on_error:
                            self.on_error(f"回复失败: {message}")
                    
                    # 随机延迟
                    delay = random.uniform(10, 30)  # 10-30秒随机延迟
                    time.sleep(delay)
                
                # 帖子间延迟
                if self.is_running and len(post_urls) > 1:
                    delay = random.uniform(60, 120)  # 1-2分钟随机延迟
                    if self.on_status_update:
                        self.on_status_update(f"等待{int(delay)}秒后处理下一个帖子...")
                    time.sleep(delay)
        
        except Exception as e:
            error_msg = f"自动回复过程中出错: {str(e)}"
            logging.error(error_msg)
            if self.on_error:
                self.on_error(error_msg)
        
        finally:
            self.is_running = False
            if self.on_status_update:
                self.on_status_update("自动回复已停止")
    
    def stop_auto_reply(self):
        """停止自动回复"""
        self.is_running = False
        if self.on_status_update:
            self.on_status_update("正在停止自动回复...")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'processed_comments_count': len(self.processed_comments),
            'reply_templates_count': len(self.reply_templates),
            'is_running': self.is_running,
            'login_status': self.get_login_status()
        }
