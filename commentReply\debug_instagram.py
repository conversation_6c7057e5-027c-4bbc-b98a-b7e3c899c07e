# -*- coding: utf-8 -*-
"""
Instagram功能调试脚本
用于独立测试Instagram功能，不依赖主GUI
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('instagram_debug.log', encoding='utf-8')
    ]
)

def test_instagram_login():
    """测试Instagram登录"""
    print("\n=== 测试Instagram登录 ===")
    
    try:
        from comment_manager import CommentManager
        manager = CommentManager()
        
        # 获取登录凭据
        username = input("请输入Instagram用户名: ").strip()
        if not username:
            print("用户名不能为空")
            return False
        
        password = input("请输入Instagram密码: ").strip()
        if not password:
            print("密码不能为空")
            return False
        
        verification_code = input("请输入验证码（如果不需要请直接回车）: ").strip()
        
        print("正在登录...")
        success, message = manager.login_instagram(username, password, verification_code)
        
        if success:
            print(f"✓ 登录成功: {message}")
            return True
        else:
            print(f"✗ 登录失败: {message}")
            return False
            
    except Exception as e:
        print(f"✗ 登录测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_url_analysis():
    """测试URL分析"""
    print("\n=== 测试URL分析 ===")
    
    try:
        from comment_manager import CommentManager
        manager = CommentManager()
        
        # 检查登录状态
        status = manager.get_login_status()
        if not status['is_logged_in']:
            print("请先登录Instagram")
            return False
        
        # 获取测试URL
        url = input("请输入Instagram帖子URL: ").strip()
        if not url:
            print("URL不能为空")
            return False
        
        if 'instagram.com' not in url:
            print("URL格式不正确")
            return False
        
        # 设置意图描述
        intent = input("请输入意图描述（例如：对杯子感兴趣）: ").strip()
        
        print("正在分析帖子...")
        
        # 设置回调函数
        def on_status_update(status):
            print(f"状态: {status}")
        
        def on_error(error):
            print(f"错误: {error}")
        
        manager.set_callbacks(
            on_status_update=on_status_update,
            on_error=on_error
        )
        
        # 分析评论
        result = manager.analyze_post_comments(url, intent, max_comments=20)
        
        if result['success']:
            print(f"✓ 分析成功: {result['message']}")
            print(f"总评论数: {len(result['comments'])}")
            print(f"目标客户数: {len(result['target_comments'])}")
            
            # 显示前几个目标客户
            for i, comment in enumerate(result['target_comments'][:3], 1):
                user = comment['user']['username']
                text = comment['text'][:50] + "..." if len(comment['text']) > 50 else comment['text']
                confidence = comment['analysis']['confidence']
                print(f"  {i}. @{user} (置信度: {confidence}): {text}")
            
            return True
        else:
            print(f"✗ 分析失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"✗ 分析测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("Instagram功能调试工具")
    print("=" * 50)
    
    # 检查依赖
    try:
        import instagrapi
        print("✓ instagrapi已安装")
    except ImportError:
        print("✗ instagrapi未安装，请运行: pip install instagrapi")
        return
    
    while True:
        print("\n请选择测试项目:")
        print("1. 测试Instagram登录")
        print("2. 测试URL分析")
        print("3. 退出")
        
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == '1':
            test_instagram_login()
        elif choice == '2':
            test_url_analysis()
        elif choice == '3':
            print("退出调试工具")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
