# -*- coding: utf-8 -*-
"""
Instagram客户端封装
用于Instagram评论获客功能
"""

import os
import json
import time
import random
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from pathlib import Path
import logging

try:
    from instagrapi import Client
    from instagrapi.exceptions import LoginRequired, PleaseWaitFewMinutes, ChallengeRequired
    INSTAGRAPI_AVAILABLE = True
except ImportError:
    INSTAGRAPI_AVAILABLE = False
    Client = None

class InstagramClient:
    """Instagram API客户端"""
    
    def __init__(self):
        self.client = None
        self.is_logged_in = False
        self.username = ""
        self.session_file = Path("instagram_session.json")
        self.last_comment_check = datetime.now()
        
        if not INSTAGRAPI_AVAILABLE:
            logging.error("instagrapi库未安装，Instagram功能将不可用")
    
    def login(self, username: str, password: str, verification_code: str = "") -> tuple[bool, str]:
        """
        登录Instagram
        
        Args:
            username: 用户名
            password: 密码
            verification_code: 验证码（如果需要）
            
        Returns:
            tuple[bool, str]: (是否成功, 消息)
        """
        if not INSTAGRAPI_AVAILABLE:
            return False, "instagrapi库未安装，请先安装: pip install instagrapi"
        
        try:
            self.client = Client()
            
            # 尝试加载已保存的会话
            if self.session_file.exists():
                try:
                    self.client.load_settings(str(self.session_file))
                    logging.info("加载已保存的会话")
                except Exception as e:
                    logging.warning(f"加载会话失败: {e}")
            
            # 尝试登录
            if verification_code:
                # 如果提供了验证码，使用验证码登录
                success = self.client.login(username, password, verification_code=verification_code)
            else:
                success = self.client.login(username, password)
            
            if success:
                self.is_logged_in = True
                self.username = username
                
                # 保存会话
                try:
                    self.client.dump_settings(str(self.session_file))
                    logging.info("会话已保存")
                except Exception as e:
                    logging.warning(f"保存会话失败: {e}")
                
                return True, "登录成功"
            else:
                return False, "登录失败，请检查用户名和密码"
                
        except ChallengeRequired as e:
            logging.error(f"需要验证: {e}")
            return False, "需要验证，请检查Instagram应用或邮箱"
        except PleaseWaitFewMinutes as e:
            logging.error(f"请稍后再试: {e}")
            return False, "请求过于频繁，请稍后再试"
        except Exception as e:
            logging.error(f"登录异常: {e}")
            return False, f"登录异常: {str(e)}"
    
    def logout(self):
        """登出"""
        self.is_logged_in = False
        self.username = ""
        self.client = None
        
        # 删除会话文件
        if self.session_file.exists():
            try:
                self.session_file.unlink()
                logging.info("会话文件已删除")
            except Exception as e:
                logging.warning(f"删除会话文件失败: {e}")
    
    def get_media_id_from_url(self, url: str) -> Optional[str]:
        """
        从Instagram帖子URL获取media_id

        Args:
            url: Instagram帖子URL

        Returns:
            Optional[str]: media_id或None
        """
        if not self.is_logged_in or not self.client:
            logging.error("Instagram客户端未登录")
            return None

        try:
            # 验证URL格式
            if not url or 'instagram.com' not in url:
                logging.error(f"无效的Instagram URL: {url}")
                return None

            logging.info(f"正在解析URL: {url}")
            media_pk = self.client.media_pk_from_url(url)
            media_id = self.client.media_id(media_pk)
            logging.info(f"成功获取media_id: {media_id}")
            return media_id
        except Exception as e:
            logging.error(f"获取media_id失败 - URL: {url}, 错误: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def get_media_comments(self, media_id: str, amount: int = 50) -> List[Dict]:
        """
        获取帖子评论
        
        Args:
            media_id: 媒体ID
            amount: 获取评论数量
            
        Returns:
            List[Dict]: 评论列表
        """
        if not self.is_logged_in or not self.client:
            return []
        
        try:
            comments = self.client.media_comments(media_id, amount)
            comment_list = []
            
            for comment in comments:
                comment_dict = {
                    'pk': comment.pk,
                    'text': comment.text,
                    'user': {
                        'pk': comment.user.pk,
                        'username': comment.user.username,
                        'full_name': comment.user.full_name,
                    },
                    'created_at': comment.created_at_utc,
                    'like_count': comment.like_count or 0,
                }
                comment_list.append(comment_dict)
            
            return comment_list
            
        except Exception as e:
            logging.error(f"获取评论失败: {e}")
            return []
    
    def reply_to_comment(self, media_id: str, text: str, replied_to_comment_id: Optional[int] = None) -> tuple[bool, str]:
        """
        回复评论
        
        Args:
            media_id: 媒体ID
            text: 回复内容
            replied_to_comment_id: 被回复的评论ID（可选）
            
        Returns:
            tuple[bool, str]: (是否成功, 消息)
        """
        if not self.is_logged_in or not self.client:
            return False, "未登录Instagram"
        
        try:
            # 添加随机延迟，模拟人类行为
            delay = random.uniform(2, 5)
            time.sleep(delay)
            
            comment = self.client.media_comment(media_id, text, replied_to_comment_id)
            
            if comment:
                logging.info(f"回复成功: {text}")
                return True, "回复成功"
            else:
                return False, "回复失败"
                
        except Exception as e:
            logging.error(f"回复评论失败: {e}")
            return False, f"回复失败: {str(e)}"
    
    def get_login_status(self) -> Dict[str, Any]:
        """
        获取登录状态信息
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            'is_logged_in': self.is_logged_in,
            'username': self.username,
            'instagrapi_available': INSTAGRAPI_AVAILABLE,
        }
