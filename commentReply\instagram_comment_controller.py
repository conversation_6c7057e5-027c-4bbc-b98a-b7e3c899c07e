# -*- coding: utf-8 -*-
"""
Instagram评论获客控制器
连接GUI界面和后端逻辑
"""

import sys
import os
import threading
import logging
from typing import List, Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

try:
    from PySide6.QtCore import QObject, Signal, QTimer
    from PySide6.QtWidgets import QMessageBox, QInputDialog, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton
except ImportError:
    print("PySide6未安装，请安装: pip install PySide6")
    sys.exit(1)

try:
    from .comment_manager import CommentManager
except ImportError:
    from comment_manager import CommentManager

class InstagramLoginDialog(QDialog):
    """Instagram登录对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Instagram登录")
        self.setModal(True)
        self.setFixedSize(350, 200)
        
        self.username = ""
        self.password = ""
        self.verification_code = ""
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 用户名输入
        username_layout = QHBoxLayout()
        username_label = QLabel("用户名:")
        username_label.setMinimumWidth(80)
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Instagram用户名")
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        layout.addLayout(username_layout)
        
        # 密码输入
        password_layout = QHBoxLayout()
        password_label = QLabel("密码:")
        password_label.setMinimumWidth(80)
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("Instagram密码")
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        layout.addLayout(password_layout)
        
        # 验证码输入（可选）
        code_layout = QHBoxLayout()
        code_label = QLabel("验证码:")
        code_label.setMinimumWidth(80)
        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("验证码（如果需要）")
        code_layout.addWidget(code_label)
        code_layout.addWidget(self.code_input)
        layout.addLayout(code_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        self.login_btn = QPushButton("登录")
        self.cancel_btn = QPushButton("取消")
        
        self.login_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.login_btn)
        layout.addLayout(button_layout)
    
    def get_credentials(self):
        """获取登录凭据"""
        return {
            'username': self.username_input.text().strip(),
            'password': self.password_input.text().strip(),
            'verification_code': self.code_input.text().strip()
        }

class InstagramCommentController(QObject):
    """Instagram评论获客控制器"""
    
    # 信号定义
    status_updated = Signal(str)
    comment_found = Signal(dict)
    reply_sent = Signal(dict)
    error_occurred = Signal(str)
    
    def __init__(self, ui_pages):
        super().__init__()
        self.ui_pages = ui_pages
        self.comment_manager = CommentManager()
        self.auto_reply_thread = None
        self.last_analysis_results = []  # 存储最后一次分析结果

        # 设置回调函数
        self.comment_manager.set_callbacks(
            on_status_update=self.on_status_update,
            on_comment_found=self.on_comment_found,
            on_reply_sent=self.on_reply_sent,
            on_error=self.on_error
        )

        # 连接信号
        self.status_updated.connect(self.update_status_display)
        self.error_occurred.connect(self.show_error_message)

        # 连接UI事件
        self.setup_ui_connections()

        # 更新登录状态
        self.update_login_status()
    
    def setup_ui_connections(self):
        """设置UI连接"""
        try:
            # 连接按钮事件
            self.ui_pages.instagram_login_btn.clicked.connect(self.show_login_dialog)
            self.ui_pages.instagram_analyze_and_reply_btn.clicked.connect(self.analyze_and_reply)
            self.ui_pages.instagram_stop_btn.clicked.connect(self.stop_auto_reply)

            logging.info("Instagram评论获客UI连接设置完成")
        except Exception as e:
            logging.error(f"设置UI连接失败: {e}")
    
    def show_login_dialog(self):
        """显示登录对话框"""
        dialog = InstagramLoginDialog()
        
        if dialog.exec() == QDialog.Accepted:
            credentials = dialog.get_credentials()
            
            if not credentials['username'] or not credentials['password']:
                QMessageBox.warning(None, "输入错误", "请输入用户名和密码")
                return
            
            # 在后台线程中执行登录
            self.login_thread = threading.Thread(
                target=self.perform_login,
                args=(credentials['username'], credentials['password'], credentials['verification_code'])
            )
            self.login_thread.daemon = True
            self.login_thread.start()
    
    def perform_login(self, username: str, password: str, verification_code: str = ""):
        """执行登录"""
        try:
            self.status_updated.emit("正在登录Instagram...")
            
            success, message = self.comment_manager.login_instagram(username, password, verification_code)
            
            if success:
                self.status_updated.emit("Instagram登录成功")
                self.update_login_status()
            else:
                self.error_occurred.emit(f"登录失败: {message}")
                
        except Exception as e:
            self.error_occurred.emit(f"登录过程中出错: {str(e)}")
    
    def update_login_status(self):
        """更新登录状态显示"""
        try:
            status = self.comment_manager.get_login_status()
            
            if status['is_logged_in']:
                self.ui_pages.instagram_login_status.setText(f"已登录: {status['username']}")
                self.ui_pages.instagram_login_status.setStyleSheet("""
                    QLabel {
                        color: #28a745;
                        font-size: 14px;
                        font-weight: bold;
                        padding: 8px;
                        background-color: #3c4454;
                        border-radius: 5px;
                    }
                """)
                self.ui_pages.instagram_login_btn.setText("重新登录")
            else:
                self.ui_pages.instagram_login_status.setText("未登录Instagram")
                self.ui_pages.instagram_login_status.setStyleSheet("""
                    QLabel {
                        color: #ff6b6b;
                        font-size: 14px;
                        font-weight: bold;
                        padding: 8px;
                        background-color: #3c4454;
                        border-radius: 5px;
                    }
                """)
                self.ui_pages.instagram_login_btn.setText("登录Instagram")
                
        except Exception as e:
            logging.error(f"更新登录状态失败: {e}")
    
    def analyze_and_reply(self):
        """分析评论并自动回复（合并功能）"""
        try:
            logging.info("开始分析评论并回复...")

            # 检查登录状态
            if not self.comment_manager.get_login_status()['is_logged_in']:
                QMessageBox.warning(None, "未登录", "请先登录Instagram")
                return

            # 获取输入内容
            urls_text = self.ui_pages.instagram_url_input.toPlainText().strip()
            intent_text = self.ui_pages.instagram_intent_input.toPlainText().strip()
            reply_text = self.ui_pages.instagram_reply_input.toPlainText().strip()

            logging.info(f"获取到URL文本: {urls_text}")
            logging.info(f"获取到意图文本: {intent_text}")
            logging.info(f"获取到回复模板: {reply_text}")

            if not urls_text:
                QMessageBox.warning(None, "输入错误", "请输入Instagram帖子URL")
                return

            if not reply_text:
                QMessageBox.warning(None, "输入错误", "请输入回复话术模板")
                return

            # 解析URL列表
            urls = [url.strip() for url in urls_text.split('\n') if url.strip()]

            logging.info(f"解析到 {len(urls)} 个URL: {urls}")

            if not urls:
                QMessageBox.warning(None, "输入错误", "请输入有效的Instagram帖子URL")
                return

            # 验证URL格式
            invalid_urls = []
            for url in urls:
                if 'instagram.com' not in url:
                    invalid_urls.append(url)

            if invalid_urls:
                QMessageBox.warning(None, "URL格式错误", f"以下URL格式不正确:\n{chr(10).join(invalid_urls)}")
                return

            # 设置意图关键词和回复模板
            if intent_text:
                keywords = intent_text.split()
                self.comment_manager.set_intent_keywords(keywords)
                logging.info(f"设置意图关键词: {keywords}")

            # 解析回复模板
            reply_templates = [template.strip() for template in reply_text.split('\n') if template.strip()]
            self.comment_manager.set_reply_templates(reply_templates)
            logging.info(f"设置回复模板: {reply_templates}")

            # 禁用按钮防止重复点击
            self.ui_pages.instagram_analyze_and_reply_btn.setEnabled(False)
            self.ui_pages.instagram_analyze_and_reply_btn.setText("正在处理...")

            # 在后台线程中执行分析和回复
            self.analyze_reply_thread = threading.Thread(
                target=self.perform_analyze_and_reply,
                args=(urls, intent_text)
            )
            self.analyze_reply_thread.daemon = True
            self.analyze_reply_thread.start()

            logging.info("分析和回复线程已启动")

        except Exception as e:
            logging.error(f"分析评论并回复失败: {e}")
            import traceback
            traceback.print_exc()
            self.error_occurred.emit(f"分析评论并回复失败: {str(e)}")
            # 恢复按钮状态
            self.ui_pages.instagram_analyze_and_reply_btn.setEnabled(True)
            self.ui_pages.instagram_analyze_and_reply_btn.setText("分析评论并回复")

    def perform_analyze_and_reply(self, urls: List[str], intent_description: str):
        """执行分析和回复"""
        try:
            # 调用合并的分析和回复方法
            self.comment_manager.analyze_and_reply(urls, intent_description)

        except Exception as e:
            logging.error(f"执行分析和回复失败: {e}")
            import traceback
            traceback.print_exc()
            self.error_occurred.emit(f"执行分析和回复失败: {str(e)}")

        finally:
            # 恢复按钮状态
            try:
                self.ui_pages.instagram_analyze_and_reply_btn.setEnabled(True)
                self.ui_pages.instagram_analyze_and_reply_btn.setText("分析评论并回复")
            except:
                pass

    def analyze_comments(self):
        """分析评论"""
        try:
            logging.info("开始分析评论...")

            # 检查登录状态
            if not self.comment_manager.get_login_status()['is_logged_in']:
                QMessageBox.warning(None, "未登录", "请先登录Instagram")
                return

            # 获取输入内容
            urls_text = self.ui_pages.instagram_url_input.toPlainText().strip()
            intent_text = self.ui_pages.instagram_intent_input.toPlainText().strip()

            logging.info(f"获取到URL文本: {urls_text}")
            logging.info(f"获取到意图文本: {intent_text}")

            if not urls_text:
                QMessageBox.warning(None, "输入错误", "请输入Instagram帖子URL")
                return

            # 解析URL列表
            urls = [url.strip() for url in urls_text.split('\n') if url.strip()]

            logging.info(f"解析到 {len(urls)} 个URL: {urls}")

            if not urls:
                QMessageBox.warning(None, "输入错误", "请输入有效的Instagram帖子URL")
                return

            # 验证URL格式
            invalid_urls = []
            for url in urls:
                if 'instagram.com' not in url:
                    invalid_urls.append(url)

            if invalid_urls:
                QMessageBox.warning(None, "URL格式错误", f"以下URL格式不正确:\n{chr(10).join(invalid_urls)}")
                return

            # 设置意图关键词
            if intent_text:
                keywords = intent_text.split()
                self.comment_manager.set_intent_keywords(keywords)
                logging.info(f"设置意图关键词: {keywords}")

            # 在后台线程中执行分析
            self.analyze_thread = threading.Thread(
                target=self.perform_analysis,
                args=(urls, intent_text)
            )
            self.analyze_thread.daemon = True
            self.analyze_thread.start()

            logging.info("分析线程已启动")

        except Exception as e:
            logging.error(f"分析评论失败: {e}")
            import traceback
            traceback.print_exc()
            self.error_occurred.emit(f"分析评论失败: {str(e)}")
    
    def perform_analysis(self, urls: List[str], intent_description: str):
        """执行评论分析"""
        try:
            all_results = []

            for url in urls:
                try:
                    self.status_updated.emit(f"正在分析帖子: {url}")

                    result = self.comment_manager.analyze_post_comments(url, intent_description)
                    all_results.append(result)

                    if not result['success']:
                        self.error_occurred.emit(f"帖子分析失败: {result['message']}")
                        continue

                except Exception as url_error:
                    logging.error(f"分析单个帖子失败 {url}: {url_error}")
                    import traceback
                    traceback.print_exc()
                    self.error_occurred.emit(f"分析帖子 {url} 失败: {str(url_error)}")
                    continue

            # 显示分析结果
            try:
                self.display_analysis_results(all_results)
                self.status_updated.emit("分析完成！可以查看结果或点击'开始回复'进行自动回复")
            except Exception as display_error:
                logging.error(f"显示分析结果失败: {display_error}")
                import traceback
                traceback.print_exc()
                self.error_occurred.emit(f"显示分析结果失败: {str(display_error)}")

        except Exception as e:
            logging.error(f"分析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            self.error_occurred.emit(f"分析过程中出错: {str(e)}")

        finally:
            # 确保分析完成后更新状态
            try:
                self.status_updated.emit("分析流程结束")
            except:
                pass
    
    def display_analysis_results(self, results: List[Dict]):
        """显示分析结果"""
        try:
            result_text = ""
            total_comments = 0
            total_targets = 0

            if not results:
                result_text = "没有分析结果"
                self.ui_pages.instagram_result_text.setPlainText(result_text)
                return

            for i, result in enumerate(results, 1):
                try:
                    if result.get('success', False):
                        comments_count = len(result.get('comments', []))
                        targets_count = len(result.get('target_comments', []))

                        total_comments += comments_count
                        total_targets += targets_count

                        result_text += f"帖子 {i}:\n"
                        result_text += f"  总评论数: {comments_count}\n"
                        result_text += f"  目标客户: {targets_count}\n"

                        # 显示前几个目标客户
                        target_comments = result.get('target_comments', [])
                        for j, comment in enumerate(target_comments[:3], 1):
                            try:
                                user = comment.get('user', {}).get('username', 'unknown')
                                text = comment.get('text', '')
                                if len(text) > 50:
                                    text = text[:50] + "..."

                                analysis = comment.get('analysis', {})
                                confidence = analysis.get('confidence', 0.0)

                                result_text += f"    {j}. @{user} (置信度: {confidence}): {text}\n"
                            except Exception as comment_error:
                                logging.error(f"处理评论显示失败: {comment_error}")
                                result_text += f"    {j}. 评论显示错误\n"

                        result_text += "\n"
                    else:
                        message = result.get('message', '未知错误')
                        result_text += f"帖子 {i}: 分析失败 - {message}\n\n"

                except Exception as result_error:
                    logging.error(f"处理单个结果失败: {result_error}")
                    result_text += f"帖子 {i}: 结果处理错误\n\n"

            result_text += f"\n总计: {total_comments}条评论，{total_targets}个潜在客户"

            # 安全地更新UI
            try:
                self.ui_pages.instagram_result_text.setPlainText(result_text)
            except Exception as ui_error:
                logging.error(f"更新UI失败: {ui_error}")

            # 保存分析结果供后续回复使用
            self.last_analysis_results = results

            self.status_updated.emit(f"分析完成: 发现{total_targets}个潜在客户")

        except Exception as e:
            logging.error(f"显示分析结果失败: {e}")
            import traceback
            traceback.print_exc()
            try:
                self.ui_pages.instagram_result_text.setPlainText(f"显示结果时出错: {str(e)}")
            except:
                pass
    
    def start_auto_reply(self):
        """开始自动回复"""
        try:
            # 获取输入内容
            urls_text = self.ui_pages.instagram_url_input.toPlainText().strip()
            intent_text = self.ui_pages.instagram_intent_input.toPlainText().strip()
            reply_text = self.ui_pages.instagram_reply_input.toPlainText().strip()
            
            if not urls_text:
                QMessageBox.warning(None, "输入错误", "请输入Instagram帖子URL")
                return
            
            if not reply_text:
                QMessageBox.warning(None, "输入错误", "请输入回复话术模板")
                return
            
            # 解析输入
            urls = [url.strip() for url in urls_text.split('\n') if url.strip()]
            reply_templates = [template.strip() for template in reply_text.split('\n') if template.strip()]
            
            # 设置回复模板
            self.comment_manager.set_reply_templates(reply_templates)
            
            # 在后台线程中执行自动回复
            self.auto_reply_thread = threading.Thread(
                target=self.comment_manager.start_auto_reply,
                args=(urls, intent_text)
            )
            self.auto_reply_thread.daemon = True
            self.auto_reply_thread.start()
            
            self.status_updated.emit("自动回复已开始...")
            
        except Exception as e:
            self.error_occurred.emit(f"启动自动回复失败: {str(e)}")
    
    def stop_auto_reply(self):
        """停止自动回复"""
        try:
            self.comment_manager.stop_auto_reply()
            self.status_updated.emit("正在停止自动回复...")

            # 恢复按钮状态
            self.ui_pages.instagram_analyze_and_reply_btn.setEnabled(True)
            self.ui_pages.instagram_analyze_and_reply_btn.setText("分析评论并回复")

        except Exception as e:
            self.error_occurred.emit(f"停止自动回复失败: {str(e)}")
    
    # 回调函数
    def on_status_update(self, status: str):
        """状态更新回调"""
        self.status_updated.emit(status)
    
    def on_comment_found(self, comment: Dict):
        """发现评论回调"""
        self.comment_found.emit(comment)
    
    def on_reply_sent(self, reply_info: Dict):
        """回复发送回调"""
        try:
            # 显示回复信息到结果区域
            comment = reply_info.get('comment', {})
            user = comment.get('user', {}).get('username', 'unknown')
            original_template = reply_info.get('original_template', '')
            reply_text = reply_info.get('reply_text', '')

            # 添加到结果显示
            current_text = self.ui_pages.instagram_result_text.toPlainText()
            new_text = f"{current_text}\n\n✅ 已回复 @{user}:\n原始模板: {original_template}\n改写后: {reply_text}\n{'-'*50}"
            self.ui_pages.instagram_result_text.setPlainText(new_text)

            # 滚动到底部
            cursor = self.ui_pages.instagram_result_text.textCursor()
            cursor.movePosition(cursor.End)
            self.ui_pages.instagram_result_text.setTextCursor(cursor)

        except Exception as e:
            logging.error(f"处理回复发送回调失败: {e}")

        self.reply_sent.emit(reply_info)
    
    def on_error(self, error_msg: str):
        """错误回调"""
        self.error_occurred.emit(error_msg)
    
    def update_status_display(self, status: str):
        """更新状态显示"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.ui_pages.instagram_status_label.setText(f"[{timestamp}] {status}")
        except Exception as e:
            logging.error(f"更新状态显示失败: {e}")
    
    def show_error_message(self, error_msg: str):
        """显示错误消息"""
        try:
            QMessageBox.critical(None, "错误", error_msg)
        except Exception as e:
            logging.error(f"显示错误消息失败: {e}")
