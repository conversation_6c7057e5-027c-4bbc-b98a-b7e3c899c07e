# -*- coding: utf-8 -*-
"""
Instagram评论获客功能测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from instagram_client import InstagramClient
        print("✓ InstagramClient导入成功")
    except ImportError as e:
        print(f"✗ InstagramClient导入失败: {e}")
        return False
    
    try:
        from ai_analyzer import AICommentAnalyzer
        print("✓ AICommentAnalyzer导入成功")
    except ImportError as e:
        print(f"✗ AICommentAnalyzer导入失败: {e}")
        return False
    
    try:
        from comment_manager import CommentManager
        print("✓ CommentManager导入成功")
    except ImportError as e:
        print(f"✗ CommentManager导入失败: {e}")
        return False
    
    return True

def test_instagram_client():
    """测试Instagram客户端"""
    print("\n测试Instagram客户端...")
    
    try:
        from instagram_client import InstagramClient
        client = InstagramClient()
        
        # 测试登录状态
        status = client.get_login_status()
        print(f"✓ 获取登录状态: {status}")
        
        # 测试URL解析（不需要登录）
        test_url = "https://www.instagram.com/p/test123/"
        print(f"✓ Instagram客户端初始化成功")
        
        return True
    except Exception as e:
        print(f"✗ Instagram客户端测试失败: {e}")
        return False

def test_ai_analyzer():
    """测试AI分析器"""
    print("\n测试AI分析器...")
    
    try:
        from ai_analyzer import AICommentAnalyzer
        analyzer = AICommentAnalyzer()
        
        # 设置测试关键词
        analyzer.set_intent_keywords(['cup', 'coffee', 'buy', '杯子', '咖啡'])
        
        # 测试评论分析
        test_comments = [
            "I love this cup! Where can I buy it?",
            "Beautiful coffee mug! How much does it cost?",
            "Nice photo!",
            "这个杯子很漂亮，哪里可以买到？"
        ]
        
        for comment in test_comments:
            result = analyzer.analyze_comment(comment, "对杯子感兴趣")
            print(f"评论: {comment}")
            print(f"  是否目标客户: {result['is_target_customer']}")
            print(f"  置信度: {result['confidence']}")
            print(f"  关键词: {result['keywords_found']}")
            print()
        
        print("✓ AI分析器测试成功")
        return True
    except Exception as e:
        print(f"✗ AI分析器测试失败: {e}")
        return False

def test_comment_manager():
    """测试评论管理器"""
    print("\n测试评论管理器...")
    
    try:
        from comment_manager import CommentManager
        manager = CommentManager()
        
        # 测试状态获取
        status = manager.get_login_status()
        print(f"✓ 获取登录状态: {status}")
        
        # 测试设置
        manager.set_intent_keywords(['test', 'keyword'])
        manager.set_reply_templates(['Thank you for your interest!', '感谢您的关注！'])
        
        # 测试统计信息
        stats = manager.get_statistics()
        print(f"✓ 获取统计信息: {stats}")
        
        print("✓ 评论管理器测试成功")
        return True
    except Exception as e:
        print(f"✗ 评论管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Instagram评论获客功能测试")
    print("=" * 50)
    
    # 检查instagrapi是否安装
    try:
        import instagrapi
        print("✓ instagrapi已安装")
    except ImportError:
        print("✗ instagrapi未安装，请运行: pip install instagrapi")
        print("  或者运行: pip install -r commentReply/requirements.txt")
        return
    
    # 运行测试
    tests = [
        test_imports,
        test_instagram_client,
        test_ai_analyzer,
        test_comment_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！Instagram评论获客功能已就绪。")
    else:
        print("✗ 部分测试失败，请检查错误信息。")

if __name__ == "__main__":
    main()
