# -*- coding: utf-8 -*-
"""
测试<PERSON>wen AI分析器功能
"""

import sys
import os
import json

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

def test_qwen_analyzer():
    """测试Qwen AI分析器"""
    print("测试Qwen AI评论分析器")
    print("=" * 60)
    
    try:
        from ai_analyzer import AICommentAnalyzer
        analyzer = AICommentAnalyzer()
        
        # 设置测试关键词
        analyzer.set_intent_keywords(['cup', 'coffee', 'buy', 'purchase', '杯子', '咖啡', '买'])
        
        # 测试评论列表
        test_comments = [
            {
                'text': "I love this cup! Where can I buy it?",
                'intent': "对咖啡杯感兴趣的客户",
                'expected': True
            },
            {
                'text': "Beautiful coffee mug! How much does it cost?",
                'intent': "对咖啡杯感兴趣的客户", 
                'expected': True
            },
            {
                'text': "Nice photo! 📸",
                'intent': "对咖啡杯感兴趣的客户",
                'expected': False
            },
            {
                'text': "这个杯子很漂亮，哪里可以买到？",
                'intent': "对杯子感兴趣的客户",
                'expected': True
            },
            {
                'text': "spam spam spam fake bot",
                'intent': "对产品感兴趣的客户",
                'expected': False
            },
            {
                'text': "😍😍😍❤️❤️❤️",
                'intent': "对产品感兴趣的客户",
                'expected': False
            },
            {
                'text': "I need a new coffee mug for my office. Do you ship internationally?",
                'intent': "对办公用品感兴趣的客户",
                'expected': True
            },
            {
                'text': "What material is this made of? I'm looking for something durable.",
                'intent': "对产品质量关心的客户",
                'expected': True
            }
        ]
        
        print("开始测试评论分析...")
        print()
        
        correct_predictions = 0
        total_tests = len(test_comments)
        
        for i, test_case in enumerate(test_comments, 1):
            comment_text = test_case['text']
            intent_desc = test_case['intent']
            expected = test_case['expected']
            
            print(f"测试 {i}/{total_tests}:")
            print(f"评论: {comment_text}")
            print(f"意图: {intent_desc}")
            print(f"预期结果: {'目标客户' if expected else '非目标客户'}")
            
            # 执行分析
            result = analyzer.analyze_comment(comment_text, intent_desc)
            
            actual = result['is_target_customer']
            confidence = result['confidence']
            reason = result['reason']
            keywords = result['keywords_found']
            method = result.get('analysis_method', 'unknown')
            
            print(f"实际结果: {'目标客户' if actual else '非目标客户'}")
            print(f"置信度: {confidence}")
            print(f"分析方法: {method}")
            print(f"原因: {reason}")
            print(f"关键词: {keywords}")
            
            # 检查预测是否正确
            if actual == expected:
                print("✓ 预测正确")
                correct_predictions += 1
            else:
                print("✗ 预测错误")
            
            print("-" * 50)
        
        # 计算准确率
        accuracy = correct_predictions / total_tests * 100
        print(f"\n测试结果总结:")
        print(f"总测试数: {total_tests}")
        print(f"正确预测: {correct_predictions}")
        print(f"准确率: {accuracy:.1f}%")
        
        if accuracy >= 75:
            print("✓ 测试通过！AI分析器表现良好")
        else:
            print("⚠ 测试结果需要改进")
        
        return accuracy >= 75
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_qwen_api_connection():
    """测试Qwen API连接"""
    print("\n测试Qwen API连接")
    print("=" * 60)
    
    try:
        from model.Qwen.qwen_api import get_qwen_response
        
        # 简单测试
        test_prompt = "请回复'连接成功'"
        response = get_qwen_response(
            prompt=test_prompt,
            temperature=0.1,
            max_tokens=50
        )
        
        print(f"测试提示: {test_prompt}")
        print(f"API响应: {response}")
        
        if "连接成功" in response or "成功" in response:
            print("✓ Qwen API连接正常")
            return True
        else:
            print("⚠ Qwen API响应异常")
            return False
            
    except Exception as e:
        print(f"✗ Qwen API连接失败: {e}")
        return False

def test_json_parsing():
    """测试JSON解析功能"""
    print("\n测试JSON解析功能")
    print("=" * 60)
    
    try:
        from ai_analyzer import AICommentAnalyzer
        analyzer = AICommentAnalyzer()
        
        # 模拟Qwen响应
        test_responses = [
            '{"is_target_customer": true, "confidence": 0.8, "reason": "表达购买意向", "keywords": ["buy", "love"]}',
            '这是一个分析结果：{"is_target_customer": false, "confidence": 0.2, "reason": "无购买意向", "keywords": []}',
            '{"is_target_customer": true, "confidence": 0.9, "reason": "强烈购买意向", "keywords": ["need", "where to buy"]}'
        ]
        
        for i, response in enumerate(test_responses, 1):
            print(f"测试响应 {i}: {response}")
            
            try:
                result = analyzer._parse_qwen_response(response, "test comment")
                print(f"解析结果: {json.dumps(result, ensure_ascii=False, default=str)}")
                print("✓ 解析成功")
            except Exception as e:
                print(f"✗ 解析失败: {e}")
            
            print("-" * 30)
        
        return True
        
    except Exception as e:
        print(f"JSON解析测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Instagram AI分析器测试套件")
    print("=" * 80)
    
    # 检查依赖
    try:
        import json
        print("✓ json模块可用")
    except ImportError:
        print("✗ json模块不可用")
        return
    
    # 运行测试
    tests = [
        ("Qwen API连接测试", test_qwen_api_connection),
        ("JSON解析测试", test_json_parsing),
        ("AI分析器功能测试", test_qwen_analyzer)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 80)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Qwen AI分析器已就绪。")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接。")

if __name__ == "__main__":
    main()
