# -*- coding: utf-8 -*-
"""
测试Instagram URL解析功能
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

def test_url_parsing():
    """测试URL解析功能"""
    print("测试Instagram URL解析功能")
    print("=" * 50)
    
    try:
        from instagram_client import InstagramClient
        client = InstagramClient()
        
        # 测试URL列表
        test_urls = [
            "https://www.instagram.com/p/ABC123/",
            "https://instagram.com/p/DEF456/",
            "https://www.instagram.com/reel/GHI789/",
            "invalid_url",
            "",
        ]
        
        print("测试URL解析（不需要登录）:")
        for url in test_urls:
            print(f"\n测试URL: {url}")
            
            if not url or 'instagram.com' not in url:
                print("  ✗ URL格式无效")
                continue
            
            try:
                # 这里只是测试URL格式验证，不实际调用API
                print("  ✓ URL格式有效")
            except Exception as e:
                print(f"  ✗ 解析失败: {e}")
        
        print("\n" + "=" * 50)
        print("URL格式验证测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_comment_analysis():
    """测试评论分析功能"""
    print("\n测试评论分析功能")
    print("=" * 50)
    
    try:
        from ai_analyzer import AICommentAnalyzer
        analyzer = AICommentAnalyzer()
        
        # 设置测试关键词
        analyzer.set_intent_keywords(['cup', 'coffee', 'buy', 'purchase', '杯子', '咖啡', '买'])
        
        # 测试评论
        test_comments = [
            "I love this cup! Where can I buy it?",
            "Beautiful coffee mug! How much?",
            "Nice photo!",
            "这个杯子很漂亮，哪里可以买到？",
            "spam spam spam",
            "😍😍😍",
        ]
        
        print("测试评论分析:")
        for comment in test_comments:
            result = analyzer.analyze_comment(comment, "对杯子感兴趣")
            print(f"\n评论: {comment}")
            print(f"  目标客户: {result['is_target_customer']}")
            print(f"  置信度: {result['confidence']}")
            print(f"  关键词: {result['keywords_found']}")
            print(f"  原因: {result['reason']}")
        
        print("\n" + "=" * 50)
        print("评论分析测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("Instagram功能调试测试")
    print("=" * 60)
    
    # 检查依赖
    try:
        import instagrapi
        print("✓ instagrapi已安装")
    except ImportError:
        print("✗ instagrapi未安装")
        return
    
    # 运行测试
    test_url_parsing()
    test_comment_analysis()
    
    print("\n" + "=" * 60)
    print("所有测试完成")

if __name__ == "__main__":
    main()
