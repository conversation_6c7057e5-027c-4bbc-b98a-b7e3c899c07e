# ======================================
# 插件基本配置 - 插件版本和基本信息
# ======================================
plugin:
  version: "v1.0.0-beta"  # 插件版本号，beta表示内测版本
  name: "Wowcker Plugin"  # 插件名称
  description: "Wowcker平台智能对话插件"  # 插件描述

# ======================================
# 文件路径配置 - 定义数据源和存储位置
# ======================================
# 源文件路径
source_file_path_core: "data/source/core"  # 核心知识库文档位置(必需)：存放重要、权威的文档
source_file_path_help: "data/source/help"  # 辅助知识库文档位置(必需)：存放辅助、补充性质的文档

# 向量数据存储路径
vector_path_core: "data/vector/core"  # 核心知识库向量存储目录(必需)：存放核心文档的向量表示
vector_path_help: "data/vector/help"  # 辅助知识库向量存储目录(必需)：存放辅助文档的向量表示

# 知识图谱存储路径
graph_file_path_core: "data/graph/core"  # 核心知识图谱路径(必需)：存放从核心文档中提取的实体关系
graph_file_path_help: "data/graph/help"  # 辅助知识图谱路径(必需)：存放从辅助文档中提取的实体关系

# ======================================
# 文本处理配置 - 控制文档分块和处理方式
# ======================================
text_processing:
  # 分块大小设置
  max_chunk_length: 500         # 文本块最大长度(字符数)：影响语义完整性和检索精度
  min_chunk_length: 200         # 文本块最小长度(字符数)：过小的块可能缺乏足够上下文
  chunk_overlap: 50             # 块重叠字符数：增加重叠可提高检索连贯性，但会增加存储量
  
  # 分隔符配置 - 决定文本如何被切分成句子
  chunk_separators:              
    # 商品分隔符 - 最强分隔符
    - "@"                        # 斜杠：商品之间的分隔符
    
    # 中文分隔符 - 强分隔符
    - "。"                       # 句号：中文句子的主要终止符
    - "！"                       # 感叹号：强终止符
    - "？"                       # 问号：强终止符
    - "\n\n"                     # 空行：段落分隔符
    - "；"                       # 分号：次要分隔符
    - "，"                       # 逗号：弱分隔符
    
    # 英文分隔符
    - ","                        # 英文逗号：弱分隔符
    - "."                        # 英文句号：强分隔符
    - "!"                        # 英文感叹号：强分隔符
    - "?"                        # 英文问号：强分隔符
  
  # 分块行为控制
  respect_sentence_boundaries: true  # 尊重句子边界：true防止在句子中间切分，保持句子完整性

# ======================================
# 知识图谱构建配置 - 控制知识图谱抽取和构建
# ======================================
knowledge_graph:
  # 处理性能设置
  use_multiprocessing: false      # 多进程设置：Excel文件处理时设为false避免内存问题
  
  # 实体提取设置
  entity_extraction:
    min_entity_freq: 2            # 实体最小出现频率：过滤低频实体
    max_entity_length: 10         # 实体最大长度(词数)：控制实体大小
    custom_entities: []           # 自定义实体列表：手动添加重要实体
  
  # 关系提取设置
  relation_extraction:
    min_relation_freq: 2          # 关系最小出现频率：过滤低频关系
    max_distance: 50              # 关系抽取最大词距离：控制关系提取范围
    custom_relations:             # 自定义关系类型：定义图谱中使用的关系类型
      - "包含"                    # 表示一个概念包含另一个概念
      - "属于"                    # 表示从属关系
      - "应用于"                  # 表示应用场景
      - "等同于"                  # 表示同义或等价关系
  
  # 图谱结构限制
  graph_structure:
    max_nodes: 10000              # 图谱最大节点数：控制图谱规模
    max_edges: 50000              # 图谱最大边数：控制图谱复杂度
  
  # 图谱可视化设置
  visualization:
    enabled: true                 # 是否启用可视化：生成图谱可视化文件
    max_vis_nodes: 100            # 可视化最大节点数：控制可视化规模
    layout: "force"               # 图布局算法：force(力导向)、circular(环形)、random(随机)

# ======================================
# 模型与向量配置 - 控制向量化和模型设置
# ======================================
# 嵌入模型配置
embedding:
  provider: "aliyun"               # 向量提供商：目前支持"aliyun"平台
  model_name: "text-embedding-v3"  # 嵌入模型：用于生成文本向量的模型名称
  api_key: "sk-adb0a59660b5458bb10e3fa2a8ab52a6"
  dashvector_endpoint: "https://dashscope.aliyuncs.com/compatible-mode/v1"  # API端点
  dimension: 1024                  # 向量维度：v3模型生成的向量维度是1024
  verbose: false                   # 详细日志：是否输出API调用详情

# 向量数据库配置
vector_db:
  type: "faiss"                    # 向量库类型：目前支持"faiss"
  index_type: "Flat"               # 索引类型：Flat(精确搜索)、IVF(近似搜索)、HNSW(图索引)等

# ======================================
# 搜索与检索配置 - 控制搜索行为和结果呈现
# ======================================
search:
  # 向量搜索配置 - 控制基于向量相似度的搜索
  vector_search:
    core_top_k: 5                  # 核心库返回结果数：从核心知识库返回的最大结果数
    help_top_k: 5                  # 辅助库返回结果数：从辅助知识库返回的最大结果数
    min_score_threshold: 0.3       # 最小相似度阈值：低于此值的结果会被过滤(范围-1到1，推荐0.6-0.7)
    content_preview_length: 10000    # 内容预览长度：搜索结果中显示的文本片段长度
  
  # 知识图谱搜索配置 - 控制基于图结构的搜索
  graph_search:
    core_top_k: 5                  # 核心图谱实体数：从核心知识图谱返回的最大实体数
    help_top_k: 5                  # 辅助图谱实体数：从辅助知识图谱返回的最大实体数
    neighbor_limit: 3              # 邻居节点限制：每个实体展示的相邻节点数量
  
  # 查询增强设置 - 控制输入查询的预处理和增强
  query_enhancement:
    enabled: false                  # 是否启用：是否对查询进行增强处理
    mode: "llm"                    # 增强模式：llm(大模型增强)、rule(规则增强)、hybrid(混合)

# ======================================
# API与服务配置 - 控制外部服务连接
# ======================================
api:
  port: 5000                       # 服务端口：本地API服务监听的端口号
  
  # 千问大模型配置 - 用于查询增强
  qwenmax:
    enabled: true                  # 是否启用：是否使用千问大模型
    api_key: "sk-adb0a59660b5458bb10e3fa2a8ab52a6"
    endpoint: "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    model: "qwen-plus-latest"              # 模型名称：可选qwen-max、qwen-plus、qwen-turbo等
    temperature: 0.1               # 温度参数：控制输出随机性，越低越确定
    max_tokens: 100                # 最大生成长度：生成内容的最大token数
    top_p: 0.8                     # 采样阈值：控制输出多样性
    
    # 查询增强提示模板 - 指导大模型如何增强查询
    prompt_template: "你是一个专业的信息检索优化助手。请将用户提出的以下问题转化为更适合信息检索的格式，满足以下要求：

1. 包含所有重要关键词
2. 去除无关词汇和停用词
3. 添加可能的同义词、缩写、相关技术术语或关联概念
4. 扩展专业术语的相关领域和应用场景
5. 考虑不同命名习惯和表达方式
6. 对于专业领域的查询，添加该领域的通用术语

例如：
- 'React怎么用' 可以转为 'React JavaScript 前端框架 使用方法 入门教程 hooks 组件'
- 'SQL注入攻击' 可以转为 'SQL注入 SQLi 数据库安全 防御方法 OWASP web漏洞'
- 'PLC编程' 可以转为 'PLC编程 可编程逻辑控制器 工业自动化 梯形图 功能块 ST结构化文本 指令列表 顺序功能图'

你的回答必须是一个查询字符串，不要有任何解释，不要使用引号。如果原始查询已经非常适合检索，可以保持不变但添加相关术语。

用户问题：{query}"
    
    # 重试设置
    retry_attempts: 2              # 重试次数：API调用失败时的重试次数
    retry_delay: 1                 # 重试间隔：两次重试之间的等待时间(秒)


