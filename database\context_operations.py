"""
上下文数据操作模块
提供contexts表的CRUD操作
"""
import logging
import threading
from typing import Dict, List, Tuple, Optional, Any
from .db_manager import DatabaseManager

logger = logging.getLogger(__name__)

class ContextOperations:
    """上下文数据操作类"""

    def __init__(self, db_manager: DatabaseManager = None):
        """
        初始化上下文数据操作

        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager or DatabaseManager()
        self._thread_local = threading.local()

    def _get_thread_safe_db_manager(self):
        """获取线程安全的数据库管理器"""
        if not hasattr(self._thread_local, 'db_manager'):
            self._thread_local.db_manager = DatabaseManager()
        return self._thread_local.db_manager
    
    def add_context(self, user_name: str, store_name: str, chat_id: str, context: str, context_trans: str = "") -> int:
        """
        添加或更新上下文

        Args:
            user_name: 用户名
            store_name: 店铺名称
            chat_id: 聊天ID
            context: 上下文内容
            context_trans: 翻译后的上下文内容

        Returns:
            int: 新上下文ID，失败返回-1
        """
        # 应用自动清理逻辑1：限制字符长度
        context = self._trim_context_if_needed(context)
        context_trans = self._trim_context_if_needed(context_trans)

        # 检查是否已存在相同的上下文记录
        existing_context = self.get_context(user_name, store_name, chat_id)

        if existing_context:
            # 如果存在，就更新
            query = """
            UPDATE contexts
            SET context = ?, context_trans = ?, update_time = CURRENT_TIMESTAMP
            WHERE user_name = ? AND store_name = ? AND chat_id = ?
            """

            # 使用线程安全的数据库管理器
            thread_db = self._get_thread_safe_db_manager()
            if not thread_db.connect():
                return -1

            try:
                thread_db.cursor.execute(query, (context, context_trans, user_name, store_name, chat_id))
                thread_db.conn.commit()
                context_id = existing_context['id']

                # 应用自动清理逻辑2：清理过期记录
                self._cleanup_expired_contexts()

                return context_id
            except Exception as e:
                logger.error(f"更新上下文时出错: {str(e)}")
                if thread_db.conn:
                    try:
                        thread_db.conn.rollback()
                    except Exception as rollback_error:
                        logger.error(f"回滚事务时出错: {str(rollback_error)}")
                return -1
            finally:
                thread_db.close()
        else:
            # 如果不存在，就添加新记录
            query = """
            INSERT INTO contexts (user_name, store_name, chat_id, context, context_trans)
            VALUES (?, ?, ?, ?, ?)
            """

            # 使用线程安全的数据库管理器
            thread_db = self._get_thread_safe_db_manager()
            if not thread_db.connect():
                return -1

            try:
                thread_db.cursor.execute(query, (user_name, store_name, chat_id, context, context_trans))
                thread_db.conn.commit()
                context_id = thread_db.get_last_insert_id()

                # 应用自动清理逻辑2：清理过期记录
                self._cleanup_expired_contexts()

                return context_id
            except Exception as e:
                logger.error(f"添加上下文时出错: {str(e)}")
                if thread_db.conn:
                    try:
                        thread_db.conn.rollback()
                    except Exception as rollback_error:
                        logger.error(f"回滚事务时出错: {str(rollback_error)}")
                return -1
            finally:
                thread_db.close()
    
    def get_context(self, user_name: str, store_name: str, chat_id: str) -> Optional[Dict]:
        """
        获取特定的上下文
        
        Args:
            user_name: 用户名
            store_name: 店铺名称
            chat_id: 聊天ID
            
        Returns:
            Optional[Dict]: 上下文信息字典
        """
        query = """
        SELECT id, user_name, store_name, chat_id, context, context_trans, update_time
        FROM contexts
        WHERE user_name = ? AND store_name = ? AND chat_id = ?
        """
        
        results = self.db_manager.execute_query(query, (user_name, store_name, chat_id))
        if not results:
            return None
        
        return {
            'id': results[0][0],
            'user_name': results[0][1],
            'store_name': results[0][2],
            'chat_id': results[0][3],
            'context': results[0][4],
            'context_trans': results[0][5],
            'update_time': results[0][6]
        }
    
    def get_contexts_by_user(self, user_name: str) -> List[Dict]:
        """
        获取用户的所有上下文
        
        Args:
            user_name: 用户名
            
        Returns:
            List[Dict]: 上下文信息列表
        """
        query = """
        SELECT id, user_name, store_name, chat_id, context, update_time
        FROM contexts
        WHERE user_name = ?
        """
        
        results = self.db_manager.execute_query(query, (user_name,))
        contexts = []
        
        for row in results:
            contexts.append({
                'id': row[0],
                'user_name': row[1],
                'store_name': row[2],
                'chat_id': row[3],
                'context': row[4],
                'update_time': row[5]
            })
        
        return contexts
    
    def get_contexts_by_store(self, store_name: str) -> List[Dict]:
        """
        获取店铺的所有上下文
        
        Args:
            store_name: 店铺名称
            
        Returns:
            List[Dict]: 上下文信息列表
        """
        query = """
        SELECT id, user_name, store_name, chat_id, context, update_time
        FROM contexts
        WHERE store_name = ?
        """
        
        results = self.db_manager.execute_query(query, (store_name,))
        contexts = []
        
        for row in results:
            contexts.append({
                'id': row[0],
                'user_name': row[1],
                'store_name': row[2],
                'chat_id': row[3],
                'context': row[4],
                'update_time': row[5]
            })
        
        return contexts
    
    def delete_context(self, user_name_or_id, store_name: str = None, chat_id: str = None) -> bool:
        """
        删除特定的上下文
        支持两种调用方式：
        1. delete_context(user_name, store_name, chat_id) - 按用户信息删除
        2. delete_context(context_id) - 按ID删除

        Args:
            user_name_or_id: 用户名或上下文ID
            store_name: 店铺名称 (按用户信息删除时必需)
            chat_id: 聊天ID (按用户信息删除时必需)

        Returns:
            bool: 操作是否成功
        """
        # 如果只传入一个参数且是整数，按ID删除
        if store_name is None and chat_id is None and isinstance(user_name_or_id, int):
            query = "DELETE FROM contexts WHERE id = ?"
            return self.db_manager.execute_update(query, (user_name_or_id,))

        # 否则按用户信息删除
        if store_name is None or chat_id is None:
            logger.error("按用户信息删除时，store_name 和 chat_id 不能为空")
            return False

        query = """
        DELETE FROM contexts
        WHERE user_name = ? AND store_name = ? AND chat_id = ?
        """

        return self.db_manager.execute_update(query, (user_name_or_id, store_name, chat_id))
    
    def delete_contexts_by_user(self, user_name: str) -> bool:
        """
        删除用户的所有上下文
        
        Args:
            user_name: 用户名
            
        Returns:
            bool: 操作是否成功
        """
        query = "DELETE FROM contexts WHERE user_name = ?"
        return self.db_manager.execute_update(query, (user_name,))
    
    def delete_contexts_by_store(self, store_name: str) -> bool:
        """
        删除店铺的所有上下文
        
        Args:
            store_name: 店铺名称
            
        Returns:
            bool: 操作是否成功
        """
        query = "DELETE FROM contexts WHERE store_name = ?"
        return self.db_manager.execute_update(query, (store_name,))

    def _trim_context_if_needed(self, context: str) -> str:
        """
        自动清理逻辑1：严格限制context字段和context_trans字段最长为50000个字符
        当字段内容达到或超过50000个字符时，保留最后49000个字符，确保严格控制

        Args:
            context: 上下文内容

        Returns:
            str: 处理后的上下文内容
        """
        if not context:
            return context

        # 严格控制在50000字符以内
        if len(context) >= 50000:
            # 保留最后49000字符，确保有1000字符的缓冲空间
            trimmed_context = context[-49000:]
            logger.info(f"上下文内容超过50000字符，已保留最后49000字符。原长度: {len(context)}, 新长度: {len(trimmed_context)}")
            return trimmed_context

        return context

    def _cleanup_expired_contexts(self) -> None:
        """
        自动清理逻辑2：删除超过72小时没有新活动的用户的所有上下文记录

        逻辑说明：
        - 按chat_id分组，找到每个用户(chat_id)的最后活动时间
        - 如果某个用户(chat_id)超过72小时没有新的上下文操作，删除该用户的所有上下文记录
        - 不会影响其他仍有活动的用户的记录
        """
        # 使用线程安全的数据库管理器
        thread_db = self._get_thread_safe_db_manager()
        if not thread_db.connect():
            logger.error("无法连接数据库进行过期上下文清理")
            return

        try:
            # 首先查找超过72小时没有活动的用户(按chat_id区分)
            find_expired_users_query = """
            SELECT user_name, store_name, chat_id, MAX(update_time) as last_activity, COUNT(*) as record_count
            FROM contexts
            GROUP BY user_name, store_name, chat_id
            HAVING MAX(update_time) < datetime('now', '-72 hours')
            """

            cursor = thread_db.cursor
            cursor.execute(find_expired_users_query)
            expired_users = cursor.fetchall()

            if not expired_users:
                logger.debug("没有发现超过72小时无活动的用户上下文")
                return

            total_deleted = 0

            # 为每个过期用户(chat_id)删除其所有上下文记录
            for user_record in expired_users:
                user_name = user_record[0]
                store_name = user_record[1]
                chat_id = user_record[2]
                last_activity = user_record[3]
                # record_count = user_record[4]  # 暂时不使用

                # 删除该用户(chat_id)在该店铺的所有上下文记录
                delete_query = """
                DELETE FROM contexts
                WHERE user_name = ? AND store_name = ? AND chat_id = ?
                """

                cursor.execute(delete_query, (user_name, store_name, chat_id))
                deleted_count = cursor.rowcount
                total_deleted += deleted_count

                logger.info(f"已清理用户 {chat_id} (用户名: {user_name}) 在店铺 {store_name} 的 {deleted_count} 条上下文记录（最后活动: {last_activity}）")

            thread_db.conn.commit()

            if total_deleted > 0:
                logger.info(f"上下文清理完成，共清理 {len(expired_users)} 个过期用户的 {total_deleted} 条记录")

        except Exception as e:
            logger.error(f"清理过期上下文时出错: {str(e)}")
            if thread_db.conn:
                try:
                    thread_db.conn.rollback()
                except Exception as rollback_error:
                    logger.error(f"回滚事务时出错: {str(rollback_error)}")
        finally:
            thread_db.close()
